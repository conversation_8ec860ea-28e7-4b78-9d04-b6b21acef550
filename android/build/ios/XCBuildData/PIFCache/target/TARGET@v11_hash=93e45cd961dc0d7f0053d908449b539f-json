{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b9b468bdb28d9514b85f21412d8ddfc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982763133bf72d54dcfdabed220fe22d7c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980604ea0e093c879e6a92511596bcecee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986703885a4593fbf8bda8323a12b58b0a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980604ea0e093c879e6a92511596bcecee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9a89f6e70dde6e791839e4011a3e54c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819c588ddbf9ddf4c0643812f20325fe0", "guid": "bfdfe7dc352907fc980b868725387e98f743f642e0bb10bdea28eb373a6497f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d4af5af54bdc79b04ebe8a14d31abf", "guid": "bfdfe7dc352907fc980b868725387e9873850dd05e8d5246a6e4d6a6425ce79c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841ce321a61415a21eedab4b128125ff3", "guid": "bfdfe7dc352907fc980b868725387e985b85d8d8951d4b1c8a946b80cce20766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230901c558bdbd0bac87a11e17293923", "guid": "bfdfe7dc352907fc980b868725387e98ebafa26ae1b1c3d2d1dd40adccb3c6db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbad27efd8c2c8a826873fd5ed563f55", "guid": "bfdfe7dc352907fc980b868725387e9864c40c4aaf7587a0258a53892392558a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345dd72e2c2c4322b80d6ed4c179b71a", "guid": "bfdfe7dc352907fc980b868725387e98930a2706a4baa2ed31225a5ae27d1b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ad4c5533b79dca1fdf13874a608846", "guid": "bfdfe7dc352907fc980b868725387e984d89a7d854c4c934eb042a72732107e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cca7223d2dde2eafa252604427c37c", "guid": "bfdfe7dc352907fc980b868725387e98f23ee5ee14b9cbe611538fcf9f2cf348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ac417a0543ca6cbfc7598ad45fbc319", "guid": "bfdfe7dc352907fc980b868725387e98875a50318a0cde71902e896165086e1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890abbfa5dc0edad5ceacc98b48bce51f", "guid": "bfdfe7dc352907fc980b868725387e9849612e63b6a55d89cc03b1cfd0ea4fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc6c1dad6c75c11e676e217dea41224", "guid": "bfdfe7dc352907fc980b868725387e98da13cb507832ed4fb4613d8cc0d55615", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c13f5ab0400c8ffe5f3891d918fd38", "guid": "bfdfe7dc352907fc980b868725387e981f49ff3e7188ae198d9d4fe2b1b1887b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491d7ca350dd8828e1bda0a74cae02ae", "guid": "bfdfe7dc352907fc980b868725387e9801900f2bde895f2142a71876877a249a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51d4de9c1078d2bc4cacf72f458582f", "guid": "bfdfe7dc352907fc980b868725387e9819de1b64edf565a285a1f2fde3b76475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858054d5337121126bbf05562100eedb2", "guid": "bfdfe7dc352907fc980b868725387e98d5603fa8fe8226349c4184161c52d4cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c0bd5ad6f0ac1baca57eef6af644dbb", "guid": "bfdfe7dc352907fc980b868725387e98ff2c6609835fb780346d293af358f39a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4c29d135e8e57736d859382c55adaf3", "guid": "bfdfe7dc352907fc980b868725387e9836b324db2467d75841af71a37b5f49c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbe0dfd4b9b4f6ffc48d0c92743997bd", "guid": "bfdfe7dc352907fc980b868725387e98b942a8fb6d79e1a826ee05d633a81a09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e61eabbb5b887d1e6b5c7f82479dd2", "guid": "bfdfe7dc352907fc980b868725387e981e4bb700cc0d2664d4a2e9c6a7d6c2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab1feb1ed72a4e3529ba10e0396f33df", "guid": "bfdfe7dc352907fc980b868725387e98c2785767e19eec920e3e7f43d73dbabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c2dbd1dee827417fec01364828c32c", "guid": "bfdfe7dc352907fc980b868725387e98ab06a5362b7a4194a27731b97e33ed55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d918429753c79b457582b03b947f8d", "guid": "bfdfe7dc352907fc980b868725387e98f08b0f6307c0e583a9a5cbfd1b07bac5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b544bf95c127bf788c0a482fc6796ef", "guid": "bfdfe7dc352907fc980b868725387e9831cb5ec77d855dc3180041bb0faae2f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eecf83e35331ab67ae6e6e4ec21b9304", "guid": "bfdfe7dc352907fc980b868725387e98ac006904d0738145273cb3cc4d1820cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05388411e1b172fd446f4e0977a8e87", "guid": "bfdfe7dc352907fc980b868725387e98a97100d8b51a3afb73228873c777dc19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfcc0c03aa86aaa91bd290b5fdda2f93", "guid": "bfdfe7dc352907fc980b868725387e98c8be10f22b6857ae33e5e911a823e5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f33dbc99ee9948c227ef060a40b8009", "guid": "bfdfe7dc352907fc980b868725387e982632446e043010887332136f78f663f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849fd06f66034e1946c6195638004b13c", "guid": "bfdfe7dc352907fc980b868725387e984edfa99db08e997706f03f96148a9de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875331364ca14d22673ea88c1046220fc", "guid": "bfdfe7dc352907fc980b868725387e980bb7f712774ad937f2b090759af15b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ae1748de2561561ff5ac78f3821c00b", "guid": "bfdfe7dc352907fc980b868725387e988704cf8084b66ff8299b81505303529e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314ae0c983d4d44a6213482cca28eb88", "guid": "bfdfe7dc352907fc980b868725387e98002f9b7c6e57968082a9ade0fe561b3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf828be7bea58f1dc4eefb87a5ae50aa", "guid": "bfdfe7dc352907fc980b868725387e98863a5e1c72e7f9e1d00d91bc8314f6b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989965d8ad08060025047fd3d779c4fa6d", "guid": "bfdfe7dc352907fc980b868725387e9840254a32d2da5d507d6b50dc1b8609e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf67d4dd6fd58b8ab897b5b32eb822e", "guid": "bfdfe7dc352907fc980b868725387e984554125c73156417e142038cf3fee4bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838ff3e1e9e30034be01cc401e1ab3bcd", "guid": "bfdfe7dc352907fc980b868725387e98c457cf5d6ffa3424e609fd50df636656"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b492a065219637b73c64254b7146bf76", "guid": "bfdfe7dc352907fc980b868725387e9869792b994c3dfcb5a9c7f2a8053db93c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd965912bf581cea62f15b83ebfe508", "guid": "bfdfe7dc352907fc980b868725387e98752da04d43fffd310ea6d11065253f56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db89606a17353682222c24826bb7c852", "guid": "bfdfe7dc352907fc980b868725387e98ccec3e11a61e8ca3a22b2afb29d8975e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc59af2c234b7822b1d82cc6415eb54d", "guid": "bfdfe7dc352907fc980b868725387e98210ed584c8dfb8be7b56ffabdaf7ad5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d4142d5997aa5eabba27adeba7aec4", "guid": "bfdfe7dc352907fc980b868725387e985454c860cc348e5355fe1704bfb9cef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd5f43065f1756314ea5422fa19ac43", "guid": "bfdfe7dc352907fc980b868725387e985bd2a309005a3a6ee59e330813ccf285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980709625fc2135b707703b3ae6743464d", "guid": "bfdfe7dc352907fc980b868725387e98e4060b9de3c31761f86ffaf0b32494ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bbf6c890d66873372366770e33abfa6", "guid": "bfdfe7dc352907fc980b868725387e9830af93b47baaf2421f9ea6d0509b7e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98182384e58e2baca4042b39fd36bc6ea6", "guid": "bfdfe7dc352907fc980b868725387e9819f561e94d7b5e3697c8228b813b8e01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880375717f01998573e554bc77335af93", "guid": "bfdfe7dc352907fc980b868725387e980d403e269581f4d9954cc878068051ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc028e4bb3d069a89f95087bf8c6e37", "guid": "bfdfe7dc352907fc980b868725387e9844fdb1063d474e62466b39e5c17e6e43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cda82b0e7a2dfcf5502cf87f6cb6be35", "guid": "bfdfe7dc352907fc980b868725387e98b619d585296a64622da04f2752d219fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e8ed2b79e25420ac2603453c7401ec", "guid": "bfdfe7dc352907fc980b868725387e9831fdc7fddf1d69e6df847d63bbcaba7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349c5b6d78527cdeb2925e6a2f90d14c", "guid": "bfdfe7dc352907fc980b868725387e98da416dbe7993bf423b0d115166f7dcc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983105914684200314b677cf413483db23", "guid": "bfdfe7dc352907fc980b868725387e983624bd2742b4562aad04961c8cd14962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966c4907214e4d7517bbf0435ee5fd64", "guid": "bfdfe7dc352907fc980b868725387e98c318c9e06c16690c95cd10fc2fc3cf2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41441d85595e9f064f85b78e166f5ae", "guid": "bfdfe7dc352907fc980b868725387e9827f71e60474be38e0c4eb8d6d7b043d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ab13a1d5b2c37d78c09565d7058b10", "guid": "bfdfe7dc352907fc980b868725387e9803e576540fb35b0c055ae7e8dace6530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9394d99a3c39df50e83d2c672085a0", "guid": "bfdfe7dc352907fc980b868725387e989a59ebb5440f9f29380615b845c44b42"}], "guid": "bfdfe7dc352907fc980b868725387e98130395da34e76e4792b036affdfe45d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a80cb1d7532265319e0467da16626df4", "guid": "bfdfe7dc352907fc980b868725387e98572bbc39e3d129060722b555d97edc88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de57dff31e37dc54229f6c14c469e925", "guid": "bfdfe7dc352907fc980b868725387e98fd435dfea6a2962f081bfa413cce8759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccb42634655065e1b434b6610d88a5dc", "guid": "bfdfe7dc352907fc980b868725387e982e56786e1ac6988ebc3953f72ae3ff6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ab2b85a12a8b858b0891169347d3dd8", "guid": "bfdfe7dc352907fc980b868725387e987036ea4b53da37417b5b4487844afc5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad96bbc3e6a65c0a446436071772dae2", "guid": "bfdfe7dc352907fc980b868725387e98d7dbe10178bf41e78bd707c1b48dd6fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f00fb5d523bcd63436d53f8b274646", "guid": "bfdfe7dc352907fc980b868725387e98c15b3e41b0ea136633016f082255faab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6c3a5550670594344a5bfd13d01776", "guid": "bfdfe7dc352907fc980b868725387e98d84273c71ae82929e3358b6f6b1d8ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d5ecf6f36f9d828a32914eebccb4a4", "guid": "bfdfe7dc352907fc980b868725387e98c167a95d9f19b215985ea19a81f1d5b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817207d3128c89d69f576d2219ce78785", "guid": "bfdfe7dc352907fc980b868725387e98130527a097876a118936369ab8c16746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ee2cce4a430da08e7ae37159c30f861", "guid": "bfdfe7dc352907fc980b868725387e98555220f937610ad915c58652e2206a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deb5be35f7ef987f7161797ae52d8af4", "guid": "bfdfe7dc352907fc980b868725387e989e5a9278036a58e734d611ad93eaaaa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a2f7967c3c7620a46b9cf7e604d580", "guid": "bfdfe7dc352907fc980b868725387e98cec0e95edc3161ab602c6cfabacc2e46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8f591405a9920c660081bc634a0744", "guid": "bfdfe7dc352907fc980b868725387e981865fd19daf112cc3b082b5ff8a47c4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d96f894a6cf0bd29bd8d173cbe4accb", "guid": "bfdfe7dc352907fc980b868725387e98f41a8fecd8af8e55fea1ada1632c2502"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85f0cfbf7b9add74bc8d4d949579485", "guid": "bfdfe7dc352907fc980b868725387e981637b0713af56fa2b1b86831d3eb2535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e6dfe7e9f888622c240b8015c78d2d", "guid": "bfdfe7dc352907fc980b868725387e98fa890f6420f3597ff66bd9b2feeeaeed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98749eae0c686b48083b225254a7b78dc8", "guid": "bfdfe7dc352907fc980b868725387e9862c7a30df000013939f8372cea53b718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869711223966b9d729d64880bce6e9a1", "guid": "bfdfe7dc352907fc980b868725387e98f54480e13250218c0bbb3771ea4d3b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d33355738d36f690d70d67c19ce7e438", "guid": "bfdfe7dc352907fc980b868725387e98dc0a48bbf857dbd5816703206eef8c47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe772908977ada5a03293001c3425d2e", "guid": "bfdfe7dc352907fc980b868725387e988ae9c10589d814ae20693598366e42ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c103aedd78aff3ed8185955adf90dc3e", "guid": "bfdfe7dc352907fc980b868725387e98a19eb866ca71b1c7fac4389617f3f73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a2e23818ea3e1ed84407445f03073b", "guid": "bfdfe7dc352907fc980b868725387e9865ed3543bb705d877d530062136da9ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9bbda46ed62229736bb962bfc61f1bd", "guid": "bfdfe7dc352907fc980b868725387e98ddad9a0d67db0af12078f29008b7d0ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2b14157c93f05c3ff5bcddd701f1096", "guid": "bfdfe7dc352907fc980b868725387e98230a970d964016b09cb75780e0abf9dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfbb14e7ab24e6e9d594f0758aa909f4", "guid": "bfdfe7dc352907fc980b868725387e983da6d012b58a9751c3bebcfbfe89e15c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcb32ca8d6485449e372422f3c91e029", "guid": "bfdfe7dc352907fc980b868725387e98c735c6f31beeb9f3182866d28f1f2f18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808be50867cd14c4bceb756a0286c57c4", "guid": "bfdfe7dc352907fc980b868725387e988aeef60bdb0c61a5e0e55c30c69f1895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd2a7d91823df0e9a8fbe2bcf2f6ae61", "guid": "bfdfe7dc352907fc980b868725387e9853c6eb566ebb7e3126deab60fd8cf0e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b06db478e2568135f6748cc0a86e72", "guid": "bfdfe7dc352907fc980b868725387e98a72018399a92c73409d526afc6fcbadf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b101c8e2b58dfa2d1ba6538c253802f7", "guid": "bfdfe7dc352907fc980b868725387e98289f8f3b8a79c1353130a7bc26fbede6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccefe7a6f16ce513a1bd298a2503b221", "guid": "bfdfe7dc352907fc980b868725387e983d2a4b148f301af496faea493ea9ed2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c86b01e6ac03238458f42d2a7b04cde", "guid": "bfdfe7dc352907fc980b868725387e989d6293d97657e5a403093d636c5c3fea"}], "guid": "bfdfe7dc352907fc980b868725387e986da509a31b8ad61fc00451cdc82ef02c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e988097c3928888d1a3f4d3de4206ac7f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e98d64555e2a9bb84f36bb4608e477b14fc"}], "guid": "bfdfe7dc352907fc980b868725387e98eeaaf5dfe29efacb1c9ad21e585a7d66", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9852c993fe86e1e2d6c00bb7a574edef13", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98ec5d9d2c7691d96d53646d538e389f26", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}