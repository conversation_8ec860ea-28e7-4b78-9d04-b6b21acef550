{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4ec13c22a9c17ad343d79f2cb78e375", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0422dd6cfbb11f0f35a5b3562acd7bc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cca09f71ac764d7ca5a4ead9ecfea169", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89f0c9b25bf705f15d6b6644540308f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cca09f71ac764d7ca5a4ead9ecfea169", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983bdf3e2fd45979d5afd651e371fa2476", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895d0d6ed37405e2e7bbe679ff03bdf40", "guid": "bfdfe7dc352907fc980b868725387e984f8cc1cf81bb571b79e6581b3908a85a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987819671997ed397a4c331be829a3cc59", "guid": "bfdfe7dc352907fc980b868725387e987ac68aece3c191598c6d703a6e07ce2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e1da156acb8fe47b17971ba7527e003", "guid": "bfdfe7dc352907fc980b868725387e988bcdfbac30d9ce92fc058200473b1dbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e9f8faa811e75c4e55a9e2518e97f0", "guid": "bfdfe7dc352907fc980b868725387e9880c331833b3ca55a2f8692d1c6190a21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a98593284e1e117af95121ea45ec714", "guid": "bfdfe7dc352907fc980b868725387e9895ee4194f87cb23bbbe60e583f39d7e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d60fbbfa5de73b4b28fc90b5037c1d4c", "guid": "bfdfe7dc352907fc980b868725387e989b2eea3c8a9c8c693e8bda1db5671ec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d36611cc7946221883701c5da6ceecaa", "guid": "bfdfe7dc352907fc980b868725387e98d44200743075a3900197743b57e28a9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98690b0be4d17a694fe9fe286ed870787f", "guid": "bfdfe7dc352907fc980b868725387e9864e6713bb433bf799674f3c9f16b9bf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f5ba4fb6ab78b1825fce069013ca8c", "guid": "bfdfe7dc352907fc980b868725387e9862b4d3074cdbba3f8e236645aff12e2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988689c0b85a436759d2d11ac6654f45ce", "guid": "bfdfe7dc352907fc980b868725387e985036fb8c12c225da235fbfbf06be5d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803f43bd4832a646aafbb1f74b8cb49eb", "guid": "bfdfe7dc352907fc980b868725387e98cd4cdc1ce0c71a18d24816f90caab039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ed4f107abf7c9168be6b06130228a5", "guid": "bfdfe7dc352907fc980b868725387e98b5af70eec39242571d7a970e9271e211", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b6503b0e43cbce43d24f27e0bf4a5f", "guid": "bfdfe7dc352907fc980b868725387e9852ad17d469daa11f9e78ee61fb9c7803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7889dc0591593a3945dc7029ab5c64a", "guid": "bfdfe7dc352907fc980b868725387e98088285c5e2070b58cb1f5b5df9e1ab8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d09066f226decb2ad9d5ad4764b8cca4", "guid": "bfdfe7dc352907fc980b868725387e98c682bee6326a2ea6882b191219f733f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adac3f0ed95133d009bf3d075a72446d", "guid": "bfdfe7dc352907fc980b868725387e989a6c7f34333bfc125380862593484486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982909258f23da42430df723a32ac83445", "guid": "bfdfe7dc352907fc980b868725387e9883830ea530c59367d00c122146eef532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d99172bc3d232be61bcd341434bd028d", "guid": "bfdfe7dc352907fc980b868725387e9802de3e9c91fba2031dd1fd1f4a835697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ad624071142ea280132448c87e2216", "guid": "bfdfe7dc352907fc980b868725387e98916d25ac03f3682d75ff0cd5a4748f7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897654570ce487110da5f22e3bb0ff3a8", "guid": "bfdfe7dc352907fc980b868725387e98848119b5d48077b872c224aa04cf4eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7c03fb17e0311041b569de42e000ce", "guid": "bfdfe7dc352907fc980b868725387e982e9b4766ca467476e379584d2c73ec77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda726b2e529b0946b9cd21a9a541526", "guid": "bfdfe7dc352907fc980b868725387e98c39bfc926ca5a1fd87898987a4272683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c175eb65d238c4427a22e54538348f3", "guid": "bfdfe7dc352907fc980b868725387e98577b22deae115561dc8fe909912b86e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d50ac3f8f30a33e69fdb896b6ffad2", "guid": "bfdfe7dc352907fc980b868725387e98f689b0d3f72518f59499ce1133ad10db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d375b0546a3cd0a3d12b66be297a116", "guid": "bfdfe7dc352907fc980b868725387e98a238af4228844442ebfcd049ed854426"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544b5ed036706cf2db465139db63cd0a", "guid": "bfdfe7dc352907fc980b868725387e982dd035c646a5042177a2479bf8e32a71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3cafc3e5fc825afa25e67e5f1f1fbd", "guid": "bfdfe7dc352907fc980b868725387e98948f314fd57e9da404b23d6b64f610f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd92c16a6f3c6fa53f639943619f55b", "guid": "bfdfe7dc352907fc980b868725387e98891f65c1f7b1d274199bf88b1428f7df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981da0832e6e0f56705a12c7ac7ae16bfe", "guid": "bfdfe7dc352907fc980b868725387e98c371e84fc5836267b7a1335be4867a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f22614303ca16223e33667f075261c", "guid": "bfdfe7dc352907fc980b868725387e98f561f09de3ed8426aee74eeebcfaa42b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd527c11e39888f951478f6ae1425b3", "guid": "bfdfe7dc352907fc980b868725387e988ef6a1a7533215935ba85c446c034696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6de081d1b5c76df5cad71fa35bba108", "guid": "bfdfe7dc352907fc980b868725387e98ddecad4fc4f85dfd09414b1943f82715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147bed0eb628a4fae178802a28d892d6", "guid": "bfdfe7dc352907fc980b868725387e982ddfdb1f695050e228dc07f150d0e58e"}], "guid": "bfdfe7dc352907fc980b868725387e98a9f48870bcdade00930021060a6d71f5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cea81772f17573ee05b0f7030d749d88", "guid": "bfdfe7dc352907fc980b868725387e988f83019a1ade3024f0b69b4770356fcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859293b1c125a1f74bcb177532d891ba1", "guid": "bfdfe7dc352907fc980b868725387e98c6cd9b48c68d96c527fddf9dcbb061de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43ecf706a1d584bb673915f09853237", "guid": "bfdfe7dc352907fc980b868725387e98c2c7a8d38ddf737b73991fdb568222b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984077a3c8f9edb3c6e903940e0ff9d1f4", "guid": "bfdfe7dc352907fc980b868725387e9889df0fae364baafc316bb2d37a8cf778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fb1d9dc70fc28b50e80241a0e70b6f2", "guid": "bfdfe7dc352907fc980b868725387e98463ae42681d210cbf199ab2d1be0ebce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc32530c94d77b62e4045276f3fcb6e5", "guid": "bfdfe7dc352907fc980b868725387e98ab2b7139a3145f769749a081c8321b0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6be3b4dd9cd6b4a01c19bd9a851fac4", "guid": "bfdfe7dc352907fc980b868725387e98d6fd9ed3bde8a9f96ac11ac8066085c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe9ebb53dff676d86f00f7af1797ca6", "guid": "bfdfe7dc352907fc980b868725387e9884367ecc6868d5310a15e408d527b504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69fb82a007bb7de2467645d19075b48", "guid": "bfdfe7dc352907fc980b868725387e9877bb6fa0b1f2fba8d4c7efdc4ff9125b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277098a944cc4b4cf9786cd5a15e5ecb", "guid": "bfdfe7dc352907fc980b868725387e98d3c861ba542281b84c89d846b269ba16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98902dbbb1674adf4bd2c5fd2b5ac8b4dc", "guid": "bfdfe7dc352907fc980b868725387e98aca984fe8d8e0b9d6114c82bae780dfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839222472f10f9c1c82cb259cfe84f98e", "guid": "bfdfe7dc352907fc980b868725387e983c75d1bb1af3b17733c016af629e6282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a9ebc2750373775de9aaeda21a8f6c", "guid": "bfdfe7dc352907fc980b868725387e989283561783f615573717d19504ef00f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f88a2fa31375e33ccfba1884a9c1e2c5", "guid": "bfdfe7dc352907fc980b868725387e98c05523592eef478fc1914f64dae6209c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986454d181a21d262e1d7b2a3fb73225ef", "guid": "bfdfe7dc352907fc980b868725387e9802b889e38819fc08868965b73f570102"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee79f3288fd965836371a7a43931b2e5", "guid": "bfdfe7dc352907fc980b868725387e98daf82e55cf9ce6da871678ccb667bf37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981216cbac82339b8d6ce95cdc05d980ca", "guid": "bfdfe7dc352907fc980b868725387e98b8550113aaa2d417a8ce6afae7098263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f3b887a6bacfe7161d88130976ebe8", "guid": "bfdfe7dc352907fc980b868725387e981e5fb9716d3561ed80a37196c79fd816"}], "guid": "bfdfe7dc352907fc980b868725387e98decde923043fb2ba47481e4daae7f416", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987ce3590a634b9a1430801fe00e1eb1ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e9898cd4e543fa1d1293908d09650e926b4"}], "guid": "bfdfe7dc352907fc980b868725387e98204031484d3011ed8a7313f9b9f64750", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98faf421c2bdfa76763c64caaf7c6f6302", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98cf36ace268d42a9e367a2c8880863093", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}