{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe1c3cc75a8c43ae15f6ffd170099a33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc062432bdd3b232fa9d03c1ab571912", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98878367b450887a7a85db4a8607de1055", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d06f57aa65a61ad5c09094a1a352f074", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98878367b450887a7a85db4a8607de1055", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98968f1d62e0506c3c105464426d22f4d9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869d197d58d6b34b6bf8c71c430d77c27", "guid": "bfdfe7dc352907fc980b868725387e98c80b755b2cd4d06255ec2116694837bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e6dd4ef175a0154f0b4f1c3689c367", "guid": "bfdfe7dc352907fc980b868725387e98dfa0a54f334b8e0e2e80a1d628e1d191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98470e2b10b1b12c7429493c13f318a26b", "guid": "bfdfe7dc352907fc980b868725387e9870a3d069cbd7027c0fbb7bdaf469f17e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811699c81e1e61546308f39f3cc789b28", "guid": "bfdfe7dc352907fc980b868725387e9848584048a8b99e0fb0fd77c1b28875a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e967c5b0d0bdfab17cf7c930a4645cf2", "guid": "bfdfe7dc352907fc980b868725387e98902a03e52f3d0126c1bb9dde7b6e7ab6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd1a025f438e5c0d7e10749044c62c0c", "guid": "bfdfe7dc352907fc980b868725387e984b9d6c70ac60aac8a5a2080058cfd1e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7dafa155d07d647a0261a0beae1aad", "guid": "bfdfe7dc352907fc980b868725387e98fb8866b2d801f7308c05bb8f0a04a089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803faeb89b56b973e6a535255c14a624d", "guid": "bfdfe7dc352907fc980b868725387e982e09ec6043ee3364857a295c34f18b17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0ed6582dc534ab3b83df7532ab9978", "guid": "bfdfe7dc352907fc980b868725387e98ba698b6c923eb7dbbdd29d33d9951c33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c7d6affd523640186a2f824e749711", "guid": "bfdfe7dc352907fc980b868725387e98ee67d3ac39021c078e57feb6558169ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8d9f81f3b89c63c36c94d7b2b1abc2", "guid": "bfdfe7dc352907fc980b868725387e989955177c3a3476990c4a46f95da3733e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f7941a018540a98f0122234b518183", "guid": "bfdfe7dc352907fc980b868725387e9869cfe27191ab5342bd5bdc5ecfcf45f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be44757c2adaf5c1bb8a88ccc59cca7e", "guid": "bfdfe7dc352907fc980b868725387e98f3b55bd49e74bc2cc58bf4ef514004d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844352c8d90ff7d53698ccdf8416079e", "guid": "bfdfe7dc352907fc980b868725387e9824a1ea28d1fb6713554c6d7be2ca9119", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890753d4c7c58d97e4772d9287b293f13", "guid": "bfdfe7dc352907fc980b868725387e9851de906b49d4d10df2740c197a20067e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ea72b985be128fc6779e4d26f07437", "guid": "bfdfe7dc352907fc980b868725387e984342f2f269b00615fb0dd92a72220e6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d041e419d9a6677fdbb02514fd8c109d", "guid": "bfdfe7dc352907fc980b868725387e9843e19d4c0503db60f1365c056c5f6fb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6252765b0a8749ca692c99b4defeb6f", "guid": "bfdfe7dc352907fc980b868725387e985e1f524e61694ddb78b7181753405363", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af272ca8112e42346453db95805552e", "guid": "bfdfe7dc352907fc980b868725387e98d91bc5331da75560376b37ce05836db6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6299dc2c39ca831c2f4137fab5797c5", "guid": "bfdfe7dc352907fc980b868725387e989fd2acdc698388499d04eb5eed897172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da93a43e91f3433a6d9ba3e2a1fc782", "guid": "bfdfe7dc352907fc980b868725387e98ff1dd3bc220de6366153e80795bf0755", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e6a6631def0a771b84771339fa722c", "guid": "bfdfe7dc352907fc980b868725387e9808cf6dc00c1d461f1706d98a67c9b698", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819efd4a57d73df2a838a4d44d2bf0477", "guid": "bfdfe7dc352907fc980b868725387e987d796904a4c86e866254af9cf5b1a562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a321439c218685d3e567d26fe252e0bc", "guid": "bfdfe7dc352907fc980b868725387e983494e0973249250426ac0436181d2186", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffdb2898acc2fde9a0fb2511fd0d1737", "guid": "bfdfe7dc352907fc980b868725387e98dfdfdbc69c12ed78e9f8371cee60b46b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984469bf729049fa67fce9909f855892b2", "guid": "bfdfe7dc352907fc980b868725387e98cac2f6083827a164d5a1e16a33e118d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1e023d7bc6dec95e664ec67931dada", "guid": "bfdfe7dc352907fc980b868725387e983c05b9d382e649b21eeedb15456dfbbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4927679563f3a72d38d915e3932d32d", "guid": "bfdfe7dc352907fc980b868725387e989bb7c2a862739bf7e9f037b2f79c46a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a916a5874c286a0f3eef56355da72e2", "guid": "bfdfe7dc352907fc980b868725387e98e278f8e0778262dc0fb6c39e0156bec8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4f2d1fda0dfa998165ccac8d5c2f300", "guid": "bfdfe7dc352907fc980b868725387e9882604236d90cc6c422e70ac26d3ffb3d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f7ff625e037a44dd890f26f1f1499543", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f815276eca573e630b21d693a8e59394", "guid": "bfdfe7dc352907fc980b868725387e98ba88c75cd918e932f830934fa565896e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7617e2b02a75c0fc7d68a43ae1a3084", "guid": "bfdfe7dc352907fc980b868725387e98ede39b211e8a7a5d942bed6a20593e4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a7fd88327a061b0ab63c7082dcae1a", "guid": "bfdfe7dc352907fc980b868725387e987d4388342c14b7f74394e10939f788db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1003aa60b7cf59531146997adfaa87", "guid": "bfdfe7dc352907fc980b868725387e98edb232036149bf82c77f85d758cd3d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b74088660421e33bb107a57d1058a86", "guid": "bfdfe7dc352907fc980b868725387e98a6df1e9c0a9dc311511c4c9eb607cef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed9c53e78be154de105b76dc90d6419c", "guid": "bfdfe7dc352907fc980b868725387e98f3e07b8437d7ded091203ecf10a67fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830f999ef1844d00860c8f06c5c9d3724", "guid": "bfdfe7dc352907fc980b868725387e98dd88ca78024592e49cac899301af23b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b287c72c75ab21a1d00e3240aa45ed1", "guid": "bfdfe7dc352907fc980b868725387e982fb35234c3d80e56be1cfd38d7dedf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e40ee6c30283d887853939c8afd129d", "guid": "bfdfe7dc352907fc980b868725387e98eebc316114ef26d69c259d2108d6d982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910a91feb086b058c8509966921096df", "guid": "bfdfe7dc352907fc980b868725387e98820551ccc4b5f677a8e970b793d6955e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab806416fd116b03a6588d1cb596a45a", "guid": "bfdfe7dc352907fc980b868725387e9866437d94b8efabdf2c1b7577d3b536e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862bbfcfeb648784cc7734a4b312e19cf", "guid": "bfdfe7dc352907fc980b868725387e98b90f37198b40f9fc09fca4ca5eab12c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a37471720fa4b7fb9b25b6e248f0a79", "guid": "bfdfe7dc352907fc980b868725387e98073cb25984f7418bd168127143ca53d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806bea7f1df79bd9620d71c8ec18dccd8", "guid": "bfdfe7dc352907fc980b868725387e985934d82fd44eb647832bb5a582719844"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a52bc2e53e0840417f25ab9f09a4b16", "guid": "bfdfe7dc352907fc980b868725387e98c7ac877ec63a84efbd3242a04c4621e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984887bca2d0248918f54aab001e0e276d", "guid": "bfdfe7dc352907fc980b868725387e98635ebf27cb7781528d0c1483604d3d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98906cac4cf78ee2f7c1822e7d7079f5cc", "guid": "bfdfe7dc352907fc980b868725387e9854e199d6e9108a7854fe8ab7fbb1c897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d495a27a325ac67fa526644c156655c9", "guid": "bfdfe7dc352907fc980b868725387e984a2560562349bec96593681775d9c22d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16b78d95619babbae02029f4c7a0594", "guid": "bfdfe7dc352907fc980b868725387e98b67c6acc120a013d791de1a1fd4673da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879636372744dcd9a3735b66cde8ec5d2", "guid": "bfdfe7dc352907fc980b868725387e98fac343f731223b53f7bf941e98823c89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d048b51073a672c6c9aeaf59c209f6d", "guid": "bfdfe7dc352907fc980b868725387e981a8bee12741634245f48cafb9459dcb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1fb85807859290e20400cab93edff17", "guid": "bfdfe7dc352907fc980b868725387e9815a43fdea0ef782dbbbaa7aa4878fb73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398cdcb2beb3f956ae58d3a608a54af9", "guid": "bfdfe7dc352907fc980b868725387e98d84dc550076c5e0903766da289ecfba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f02ca1a77ad2becef9a546a5797a9a57", "guid": "bfdfe7dc352907fc980b868725387e98e83f923c7c3e15c8dcabf7695689a865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d4b2b4be69a2e8529d9e1cc60e772c", "guid": "bfdfe7dc352907fc980b868725387e98e74f9d91ff6ae221091ec1feaf748a7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cceee4c2bb58bd6e551b22cb6f9f9eef", "guid": "bfdfe7dc352907fc980b868725387e98f518e3f2cdc45f1829f7136c2de8380b"}], "guid": "bfdfe7dc352907fc980b868725387e98d65b67eecf4f254aab7b09b9a60a037a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98f3c17975a5153dc4464a3980eadf9abd"}], "guid": "bfdfe7dc352907fc980b868725387e980411904fc396afcc44d9b145967dfc5b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c3145dd5eb2b443235fa1f71f8e35731", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98b6a2337f9a982e87f75ac01776267149", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}