import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/task_model.dart';
import 'auth_service.dart';

/// Production-ready Task Service for Firebase operations
class TaskService extends GetxService {
  static TaskService get instance => Get.find();

  final AuthService _authService = AuthService.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference
  static const String _tasksCollection = 'tasks';

  /// Create a new task
  Future<TaskModel> createTask({
    required String projectId,
    required String title,
    required String description,
    required TaskPriority priority,
    String? assignedTo,
    DateTime? dueDate,
    List<String>? tags,
  }) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      final now = DateTime.now();
      final task = TaskModel(
        id: '', // Will be set by Firestore
        projectId: projectId,
        title: title.trim(),
        description: description.trim(),
        status: TaskStatus.todo,
        priority: priority,
        assignedTo: assignedTo,
        assignedBy: assignedTo != null ? currentUser.uid : null,
        createdBy: currentUser.uid,
        dueDate: dueDate,
        tags: tags ?? [],
        createdAt: now,
        updatedAt: now,
      );

      // Add to Firestore
      final docRef = await _firestore
          .collection(_tasksCollection)
          .add(task.toFirestore());

      // Return task with the generated ID
      final createdTask = task.copyWith(id: docRef.id);

      // Update project stats
      await _updateProjectStats(projectId);

      if (Get.isLogEnable) {
        Get.log('Task created successfully: ${createdTask.id}');
      }

      return createdTask;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error creating task: $e');
      }
      rethrow;
    }
  }

  /// Get all tasks for a project
  Future<List<TaskModel>> getProjectTasks(String projectId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_tasksCollection)
          .where('projectId', isEqualTo: projectId)
          .get();

      final tasks = querySnapshot.docs
          .map((doc) => TaskModel.fromFirestore(doc))
          .toList();

      // Sort locally by priority and due date
      tasks.sort((a, b) {
        // First sort by status (todo, inProgress, completed)
        final statusOrder = {
          TaskStatus.inProgress: 0,
          TaskStatus.todo: 1,
          TaskStatus.completed: 2,
        };

        final statusComparison = (statusOrder[a.status] ?? 3).compareTo(
          statusOrder[b.status] ?? 3,
        );

        if (statusComparison != 0) return statusComparison;

        // Then by priority (urgent, high, medium, low)
        final priorityOrder = {
          TaskPriority.urgent: 0,
          TaskPriority.high: 1,
          TaskPriority.medium: 2,
          TaskPriority.low: 3,
        };

        final priorityComparison = (priorityOrder[a.priority] ?? 4).compareTo(
          priorityOrder[b.priority] ?? 4,
        );

        if (priorityComparison != 0) return priorityComparison;

        // Finally by due date (earliest first)
        if (a.dueDate != null && b.dueDate != null) {
          return a.dueDate!.compareTo(b.dueDate!);
        } else if (a.dueDate != null) {
          return -1;
        } else if (b.dueDate != null) {
          return 1;
        }

        // If no due date, sort by creation date (newest first)
        return b.createdAt.compareTo(a.createdAt);
      });

      if (Get.isLogEnable) {
        Get.log('Fetched ${tasks.length} tasks for project: $projectId');
      }

      return tasks;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching project tasks: $e');
      }
      rethrow;
    }
  }

  /// Get tasks assigned to current user
  Future<List<TaskModel>> getMyTasks() async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      final querySnapshot = await _firestore
          .collection(_tasksCollection)
          .where('assignedTo', isEqualTo: currentUser.uid)
          .get();

      final tasks = querySnapshot.docs
          .map((doc) => TaskModel.fromFirestore(doc))
          .toList();

      // Sort by due date and priority
      tasks.sort((a, b) {
        if (a.dueDate != null && b.dueDate != null) {
          return a.dueDate!.compareTo(b.dueDate!);
        } else if (a.dueDate != null) {
          return -1;
        } else if (b.dueDate != null) {
          return 1;
        }
        return b.createdAt.compareTo(a.createdAt);
      });

      if (Get.isLogEnable) {
        Get.log(
          'Fetched ${tasks.length} tasks assigned to user: ${currentUser.uid}',
        );
      }

      return tasks;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching user tasks: $e');
      }
      rethrow;
    }
  }

  /// Update a task
  Future<void> updateTask(TaskModel task) async {
    try {
      final updatedTask = task.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_tasksCollection)
          .doc(task.id)
          .update(updatedTask.toFirestore());

      // Update project stats if status changed
      await _updateProjectStats(task.projectId);

      if (Get.isLogEnable) {
        Get.log('Task updated successfully: ${task.id}');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error updating task: $e');
      }
      rethrow;
    }
  }

  /// Delete a task
  Future<void> deleteTask(String taskId, String projectId) async {
    try {
      await _firestore.collection(_tasksCollection).doc(taskId).delete();

      // Update project stats
      await _updateProjectStats(projectId);

      if (Get.isLogEnable) {
        Get.log('Task deleted successfully: $taskId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error deleting task: $e');
      }
      rethrow;
    }
  }

  /// Get real-time stream of project tasks
  Stream<List<TaskModel>> getProjectTasksStream(String projectId) {
    return _firestore
        .collection(_tasksCollection)
        .where('projectId', isEqualTo: projectId)
        .snapshots()
        .map((snapshot) {
          final tasks = snapshot.docs
              .map((doc) => TaskModel.fromFirestore(doc))
              .toList();

          // Sort tasks (same logic as getProjectTasks)
          tasks.sort((a, b) {
            final statusOrder = {
              TaskStatus.inProgress: 0,
              TaskStatus.todo: 1,
              TaskStatus.completed: 2,
            };

            final statusComparison = (statusOrder[a.status] ?? 3).compareTo(
              statusOrder[b.status] ?? 3,
            );

            if (statusComparison != 0) return statusComparison;

            final priorityOrder = {
              TaskPriority.urgent: 0,
              TaskPriority.high: 1,
              TaskPriority.medium: 2,
              TaskPriority.low: 3,
            };

            return (priorityOrder[a.priority] ?? 4).compareTo(
              priorityOrder[b.priority] ?? 4,
            );
          });

          return tasks;
        });
  }

  /// Update project statistics
  Future<void> _updateProjectStats(String projectId) async {
    try {
      final tasks = await getProjectTasks(projectId);
      final totalTasks = tasks.length;
      final completedTasks = tasks.where((task) => task.isCompleted).length;

      await _firestore.collection('projects').doc(projectId).update({
        'stats.totalTasks': totalTasks,
        'stats.completedTasks': completedTasks,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error updating project stats: $e');
      }
    }
  }

  /// Assign task to user
  Future<void> assignTask(String taskId, String userId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      await _firestore.collection(_tasksCollection).doc(taskId).update({
        'assignedTo': userId,
        'assignedBy': currentUser.uid,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      if (Get.isLogEnable) {
        Get.log('Task assigned successfully: $taskId to $userId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error assigning task: $e');
      }
      rethrow;
    }
  }

  /// Update task status
  Future<void> updateTaskStatus(
    String taskId,
    TaskStatus status,
    String projectId,
  ) async {
    try {
      await _firestore.collection(_tasksCollection).doc(taskId).update({
        'status': status.value,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Update project stats
      await _updateProjectStats(projectId);

      if (Get.isLogEnable) {
        Get.log('Task status updated: $taskId to ${status.value}');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error updating task status: $e');
      }
      rethrow;
    }
  }
}
