abstract class AppRoutes {
  // Authentication Routes
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';

  // Main App Routes
  static const String home = '/home';
  static const String main = '/main';

  // Project Routes
  static const String projects = '/projects';
  static const String projectDetails = '/project-details';
  static const String createProject = '/create-project';
  static const String editProject = '/edit-project';
  static const String joinRequests = '/join-requests';
  static const String discoverProjects = '/discover-projects';

  // Task Routes
  static const String tasks = '/tasks';
  static const String taskDetails = '/task-details';
  static const String createTask = '/create-task';
  static const String editTask = '/edit-task';

  // Profile Routes
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String profileSetup = '/profile-setup';

  // Settings Routes
  static const String settings = '/settings';

  // Splash Route
  static const String splash = '/splash';

  // Initial Route
  static const String initial = splash;
}
