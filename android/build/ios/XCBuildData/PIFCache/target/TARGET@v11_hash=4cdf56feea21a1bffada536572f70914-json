{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d1df9f7ce21231be8b5a53b90c483c6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e5063d1a61d6abe1341a8689b7308092", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a46568b5a1aa7fe05ecb80f19c511a3a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e7100f5d3398fa477fc3e3f3ef0dc26", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a46568b5a1aa7fe05ecb80f19c511a3a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e6820c95f107673cacc87a4262286a75", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc98dd29744e0d70d3a69bab5919f001", "guid": "bfdfe7dc352907fc980b868725387e98127341cdfea532fbe80233248b178efa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985560bd8f58d056f2942ae0ad4a71066d", "guid": "bfdfe7dc352907fc980b868725387e987a93cf0ff958cecf4aa4c323ff0af598", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f715c09b6e692244c58203b3cc4c7152", "guid": "bfdfe7dc352907fc980b868725387e983583e138cb3785004831d738976ad01a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980270e4b693867cb705226ed9bda42385", "guid": "bfdfe7dc352907fc980b868725387e987ab641334f6004357012d5becd1ad458", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e190537f428a7abf1b94f3ae6d4334eb", "guid": "bfdfe7dc352907fc980b868725387e98be901ea68e5937e2a418b5bf1cc78be0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b0587134a896aa48dfe3f5c73cdbf9", "guid": "bfdfe7dc352907fc980b868725387e98865e2f6dd9e7dde684f6181f8450f49f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f44b2104010446ffded8baaea5d4f8", "guid": "bfdfe7dc352907fc980b868725387e98b3263f18ca185011bc41da587af91e54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475d548c812a9b92ef89f768ff5d1693", "guid": "bfdfe7dc352907fc980b868725387e98eb8a7587646dfc8863672247f3f7a627", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbf853cf35d612ae1c13f4e42cc7cd8", "guid": "bfdfe7dc352907fc980b868725387e982c63c6579192be5d77cc99b015f47332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062c9087af870ee391bb5dbc96ffcd41", "guid": "bfdfe7dc352907fc980b868725387e9882898feb57809f7697aefcbef71e0ce9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a354faa25ae926e8d6839483e587c0f", "guid": "bfdfe7dc352907fc980b868725387e98ee7eb8caf9fa3d0b707c70dc037bda88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6ba23c21aa1438e7666d78bb3babe2", "guid": "bfdfe7dc352907fc980b868725387e987c5aaadf7490dfb231b9019083be502c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e41770b777a35d1786dc5ab7589b806", "guid": "bfdfe7dc352907fc980b868725387e9854fd2faf8f57e3a53bb12d0099fb4da3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa2377a3696560ad7fd35b1a0ec2dff", "guid": "bfdfe7dc352907fc980b868725387e980af16a7477494cba1e6c8fa961e82d09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e9a80aa71c6e0c7d24e42d252b68ff", "guid": "bfdfe7dc352907fc980b868725387e98891608fb45e4f5752788426fc07b987a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ee793d11d05f98db5d00dbeafe57f8a", "guid": "bfdfe7dc352907fc980b868725387e984bb0b9e2ab27d542d4c570a758045ec0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98976009c475f7b0fc3ac9eff6831a6d91", "guid": "bfdfe7dc352907fc980b868725387e98cde9299e2e6d6b39f41bcf65fb6e3ff2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820bd377ff93d3f6fec62496b649f75b3", "guid": "bfdfe7dc352907fc980b868725387e9895190d04b54af5ebf9a4d921a4cc4353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb76deeca28c9df23cf97528ea4960d", "guid": "bfdfe7dc352907fc980b868725387e9800fae910fede55e6c959ce4194a79ae9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989861a6e284d3020b2453f81f47749a32", "guid": "bfdfe7dc352907fc980b868725387e983bff70c372d49101a30f29e3d0f6864a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd706a3d27c4e00f217e009b3cdecc7", "guid": "bfdfe7dc352907fc980b868725387e985edd60db48f4e4b969edd6699277e6e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6101f98aa0be4e07179b85326c4e75", "guid": "bfdfe7dc352907fc980b868725387e982561ab9e31e00802449bd75e1bcd18eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c34850a421e20627884cb36b448fb21c", "guid": "bfdfe7dc352907fc980b868725387e98d44e391bde73d5b1d34f052086748dc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab0c7891f80996ae8d18ddd42b4007e", "guid": "bfdfe7dc352907fc980b868725387e9817914a188ebe1a2a0d92ed129a1e94e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9076be329e01f331d013e29646fa45", "guid": "bfdfe7dc352907fc980b868725387e9891c304b99dbd6e12fb8470f4accbdfda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a457db6df0b941e05d240f16ad81f36", "guid": "bfdfe7dc352907fc980b868725387e98478d6d45b75db3c12c09a3d962c0ffad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f268b64107131af0f8bd9309c0a119a9", "guid": "bfdfe7dc352907fc980b868725387e982917e0a4ec3722cff8657723c498b81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830aff86cb8037a9ee4c9fa8f1947cdbe", "guid": "bfdfe7dc352907fc980b868725387e984efefe8c95c3ddb818d7e8c319217792", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239d10f172944c6029ff60304cf9f963", "guid": "bfdfe7dc352907fc980b868725387e98373a10cf03528907001f77660d93c457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eedd594afe841bbf040a8feda81a7c8", "guid": "bfdfe7dc352907fc980b868725387e9852a43e92fd23361d12deb92e0181a354", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8d94cb3da86fc08f5edb049941e44d6", "guid": "bfdfe7dc352907fc980b868725387e9848a29cdc192bac390271675b4ac8d08f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc77d04f765654f8238a15ec7a183a7", "guid": "bfdfe7dc352907fc980b868725387e98c608cbaf8e71e598a427a3abcc868f23", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985f9ddc91dbba1ae0b3682a6f09ce38c8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896970225270253433b2b50abca6c1783", "guid": "bfdfe7dc352907fc980b868725387e989bbe2d9b8e47a7688b8e9460583c0634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8c84dfb7fc762b1681f71e465deef8", "guid": "bfdfe7dc352907fc980b868725387e98b51313b55cbd7282e24231088703a038"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c63a1f6a8ba71e40ba9772415c1fc9b", "guid": "bfdfe7dc352907fc980b868725387e9825c8dde55e7db75b6f120dfd9de8672d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0bb62ba1481e8c2c69fe5cea24479c", "guid": "bfdfe7dc352907fc980b868725387e98d4c51e2dc69981de635c1d8f8f3af5b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed582e251ab293d55217a701afe5731", "guid": "bfdfe7dc352907fc980b868725387e98093a5a6cc932876831ea2d3da41b1319"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bbc8125285a4e3e31796e34f0d336f1", "guid": "bfdfe7dc352907fc980b868725387e981f1f1ee17fb936281fc33154aa91af47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899c757d61043752a5b7f46fbdc53af54", "guid": "bfdfe7dc352907fc980b868725387e9877e44bd9e3af227c0075526664a83584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98295c2d8c363c95f7e459d2565634d109", "guid": "bfdfe7dc352907fc980b868725387e9851a3444c9a271a5989e7f78afab9aeb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6593db0558cc54452d98100a3ee1498", "guid": "bfdfe7dc352907fc980b868725387e984424d50b9086f42baadc04b5ac1dd8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a805a38cc5f20cd6ff4cd13d1ec123", "guid": "bfdfe7dc352907fc980b868725387e98b11d561b24d95a434170fac745f3d88d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2ae202365e3b921543e493565fb39c", "guid": "bfdfe7dc352907fc980b868725387e98ff485dc1676f63d5ab4d36a9a1b6c79a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98649e9056dcc0a6cc09f37bfbad6c6c07", "guid": "bfdfe7dc352907fc980b868725387e98a22ca59a20b66dde1b53230a5978ef9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a40a52d1ecd480b69b69289ce384557a", "guid": "bfdfe7dc352907fc980b868725387e98aadb2fe957727e072c8a9a47d4be44e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b20e2084ed9d4359fcd1759b337847", "guid": "bfdfe7dc352907fc980b868725387e98e05b4963130e5dcecad027d8c77bd4a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db910ba28c1a6e7af8f5300ddee0b969", "guid": "bfdfe7dc352907fc980b868725387e98626f34a9030bacf635aefb3da4df6b63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bfe26cda11b36ab72f11cbcac8da5fb", "guid": "bfdfe7dc352907fc980b868725387e98451c8814f9ab2b15484caee48b30e87e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b0fccb6f855234214947fe3f7236b8b", "guid": "bfdfe7dc352907fc980b868725387e989988d68788f912b7b78c2bfd00970fa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af2d10e3b54b3e0b5e1d3e45aab38da", "guid": "bfdfe7dc352907fc980b868725387e984f7086252b8a3f603ee5252d09e4609e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a9ca9476fcabfa23fda2002da48371", "guid": "bfdfe7dc352907fc980b868725387e981a2d8f952827baa6f156d69d4779dd39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bfdaf3fd970ab167e99e77431fe7e80", "guid": "bfdfe7dc352907fc980b868725387e98ab8c6a2d5c2ee36860b96d0ec480e802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160a396cf2239a0f101c9a85614db5db", "guid": "bfdfe7dc352907fc980b868725387e9841e8a622654e8ab7fa382463280ca6a2"}], "guid": "bfdfe7dc352907fc980b868725387e9806e550f18e092f22deb2ea65772dc8cc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9860ae3c29b20f5c09409d0208c4a1ae5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e98f9e612c273846a49a2c7ee4214f1e1dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e9823e74144508dc364bcd51451ca2b542c"}], "guid": "bfdfe7dc352907fc980b868725387e98b7166cc28779ee3bbca2f5f49959f7ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d1020164e3acd9d155a4194a048a6f1c", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e980e2b886176d6dbbb3cf4306ec69ae960", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}