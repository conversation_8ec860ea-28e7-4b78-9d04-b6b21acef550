{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d475408af70c3314186da9a62b8d14b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0e3975817bab5323e5a1816dfb10a6c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869fba4d175b3dae0da1b94abb8a49945", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f09a48bab116c9382bf318a609ceca7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869fba4d175b3dae0da1b94abb8a49945", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98950197634742aac71b9c16eb4eab9277", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872a183274103930e672093b72c6c9c8d", "guid": "bfdfe7dc352907fc980b868725387e98a9942935d2a10b6334b0d32b59fc5354"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd326028ae34055bfe7651984023432", "guid": "bfdfe7dc352907fc980b868725387e98db9c63720be534625864f4789a535da8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b280f4f754b279d6d6807cc3c6b583f2", "guid": "bfdfe7dc352907fc980b868725387e982d79a7721ba22a8af16d8db62e55b1f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca21eaf455fe83b901661373853ea30", "guid": "bfdfe7dc352907fc980b868725387e98094279e1b11944dc8848a8c65e75f7ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e494dcba6d937cc52ae0591ee4092824", "guid": "bfdfe7dc352907fc980b868725387e986021ec8237f5d19e76105642c64973e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b120c1f6b53e5cc776132ba11782c72", "guid": "bfdfe7dc352907fc980b868725387e9810ec7fbf5f384072cd4e1173fa6141b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4d759a3485da8c7db5272d1a507dd8", "guid": "bfdfe7dc352907fc980b868725387e988983c65cdcf886b1166824f15af9dadf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c49905a61f4e2913568422ecf8c3fec", "guid": "bfdfe7dc352907fc980b868725387e98bc5ee0fcc42c7645ed4f00e3cc3b8567"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2379fb2e26f5a9269d291c8e87efa5", "guid": "bfdfe7dc352907fc980b868725387e98cfe58de5ddc3e3ccdb62d7542734f9ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c308b54d7ce11bd42ee9c0a810c48867", "guid": "bfdfe7dc352907fc980b868725387e9801bc78a8d03af4af15a7f2f44d6b296b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d005414ed3b6268e5143cb1eaa916a", "guid": "bfdfe7dc352907fc980b868725387e98749e2be3a562bedeec1d58d0ca933132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2d75deca890d769499b8131fccd2ab", "guid": "bfdfe7dc352907fc980b868725387e98e1f1eeb3d862cad7c279398561bc423a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b996d6597569426511cf061615ccf3e6", "guid": "bfdfe7dc352907fc980b868725387e982cb8e099238710051ce168296cc866fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580296e95cb6121bf75ded203f60462f", "guid": "bfdfe7dc352907fc980b868725387e98c442385147bdf1a275afc294d43e4f08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988340da48c782e1a8867adc2c949c330e", "guid": "bfdfe7dc352907fc980b868725387e9840cccbd37c8db54f0720fb435f1ee483"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da02f09c162bb1d8c21a196efee58abe", "guid": "bfdfe7dc352907fc980b868725387e98234701cc6468aee3810d4547801c7ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025bf929d35ce4bc0cab07f38150f304", "guid": "bfdfe7dc352907fc980b868725387e989a0f5e118375078c36e5915f9e8ce996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873487ee2828c69f166b9fe777a1b9e03", "guid": "bfdfe7dc352907fc980b868725387e98f7aee030761b524197c690a36311fca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4ce95e3ce0cf8a10c0ad41e8df9657", "guid": "bfdfe7dc352907fc980b868725387e98176a9c3fb5490fdccde71db5000f2f72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c83df83c78b3466fc96206651f4177aa", "guid": "bfdfe7dc352907fc980b868725387e98fa67bc567f86b3d9e65993f641c119e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d34e4e4a37cc429e15df4713e00d8a2", "guid": "bfdfe7dc352907fc980b868725387e9885addbe5a0e78e264c9612e38cb38a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46454f54aa75ca03bc21ca6c4779f99", "guid": "bfdfe7dc352907fc980b868725387e9876dc209fba88e5fff69ec28c1b6662e0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9844b353251bbe7615ae21cd4f9ceeb31c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829d3eccdcb85b2b971f1d3e2c43db601", "guid": "bfdfe7dc352907fc980b868725387e98e66cfbf5541c99b8918b58b748382b86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b952e9245d42f8e528edfb74728cb08e", "guid": "bfdfe7dc352907fc980b868725387e98294e82cdb806dac6ca3e5d2075248594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1867d8d96bb49a1b1035b8a6f9a432", "guid": "bfdfe7dc352907fc980b868725387e988872411dac7d76bc73ae383e3372dc40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b58634d1d71e14e71dd3718ebb974317", "guid": "bfdfe7dc352907fc980b868725387e98c0ec80d14f78ef7d1291abcb62af20fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988edf1116ae125adad273438795d6949a", "guid": "bfdfe7dc352907fc980b868725387e988d5723b0466306a2bba01320498c20c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864532b74c0f9dc29631ec418aaed19e3", "guid": "bfdfe7dc352907fc980b868725387e98422297a84869a6d891f3e0b32ecb89c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d36cf5ad5b33470e43917a25f3f8a4a0", "guid": "bfdfe7dc352907fc980b868725387e983feeab39449afeb4a1bfa76c96db0d2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25af47f5a6994b91691116ea2410217", "guid": "bfdfe7dc352907fc980b868725387e98f1a8916b983ffb9464757fea2ec7fc67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1017c728458a3d7da2eed959f84d1f0", "guid": "bfdfe7dc352907fc980b868725387e982b7fc02eb47e32c5a9e1d998b21bb3a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b2be71399e724523205a16f8dfe1ed", "guid": "bfdfe7dc352907fc980b868725387e9894158ca6aa0acbaf47cd0945363f5559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509ce830eec8359d7267bd4d62fd59c3", "guid": "bfdfe7dc352907fc980b868725387e98a76d3fcb8db119048455832e2fa03795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5143020955fcc0b3a5dd46f0eefd2be", "guid": "bfdfe7dc352907fc980b868725387e98e9ded5eb3cd6c7aaa9f60bbb5ccf61d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98758785db3be2ebc870f4af5203380eea", "guid": "bfdfe7dc352907fc980b868725387e98aa9301e20acd173a6d04dc5046861384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d31b7b4ad4df534283a43662125657", "guid": "bfdfe7dc352907fc980b868725387e98e5423679d30b7b9cfc63d477db68672b"}], "guid": "bfdfe7dc352907fc980b868725387e982a00394443d7d3e4aed14a0f865e6409", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98a835db0d4ddb1b06b0651942d5e1cb83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e51514501e0f0d62f7ef6096d26bdc0", "guid": "bfdfe7dc352907fc980b868725387e98d1eccdc4ed12a448bb42c446651743b2"}], "guid": "bfdfe7dc352907fc980b868725387e98f211ee7c058471ec17d358d9f00d6a3e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987931c32e70eabdb72b2eeb4a74b73c73", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9867765360752d379fef4180105d71ed0d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}