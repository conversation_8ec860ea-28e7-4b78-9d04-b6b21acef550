import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_setup_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/gradient_background.dart';

class ProfileSetupView extends GetView<ProfileSetupController> {
  const ProfileSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Header
                Text(
                  'Complete Your Profile',
                  style: Get.textTheme.displayMedium?.copyWith(
                    color: AppColors.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Help others know more about you and your professional background',
                  style: Get.textTheme.bodyLarge?.copyWith(
                    color: AppColors.onPrimary.withValues(alpha: 0.8),
                  ),
                ),

                const SizedBox(height: 40),

                // Profile form
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowMedium,
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Form(
                    key: controller.profileFormKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Company name
                        CustomTextField(
                          controller: controller.companyNameController,
                          labelText: 'Company Name',
                          hintText: 'Enter your company name',
                          prefixIcon: Icons.business_outlined,
                          validator: controller.validateCompanyName,
                        ),

                        const SizedBox(height: 16),

                        // Job title
                        CustomTextField(
                          controller: controller.jobTitleController,
                          labelText: 'Job Title',
                          hintText: 'Enter your job title',
                          prefixIcon: Icons.work_outline,
                          validator: controller.validateJobTitle,
                        ),

                        const SizedBox(height: 16),

                        // Phone number
                        CustomTextField(
                          controller: controller.phoneNumberController,
                          labelText: 'Phone Number',
                          hintText: 'Enter your phone number',
                          keyboardType: TextInputType.phone,
                          prefixIcon: Icons.phone_outlined,
                          validator: controller.validatePhoneNumber,
                        ),

                        const SizedBox(height: 16),

                        // Bio
                        CustomTextField(
                          controller: controller.bioController,
                          labelText: 'Bio (Optional)',
                          hintText: 'Tell us about yourself...',
                          maxLines: 3,
                          prefixIcon: Icons.person_outline,
                          validator: controller.validateBio,
                        ),

                        const SizedBox(height: 24),

                        // Skills section
                        Text(
                          'Skills',
                          style: Get.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Add skill field
                        Row(
                          children: [
                            Expanded(
                              child: CustomTextField(
                                controller: controller.skillController,
                                labelText: 'Add Skill',
                                hintText: 'e.g., Flutter, Firebase, etc.',
                                prefixIcon: Icons.code_outlined,
                                onSubmitted: (_) => controller.addSkill(),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: controller.addSkill,
                              icon: const Icon(Icons.add_circle_outline),
                              color: AppColors.accent,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Skills chips
                        Obx(
                          () => controller.skills.isEmpty
                              ? Text(
                                  'No skills added yet',
                                  style: Get.textTheme.bodyMedium?.copyWith(
                                    color: AppColors.onSurfaceVariant,
                                  ),
                                )
                              : Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: controller.skills
                                      .map(
                                        (skill) => Chip(
                                          label: Text(skill),
                                          deleteIcon: const Icon(
                                            Icons.close,
                                            size: 18,
                                          ),
                                          onDeleted: () =>
                                              controller.removeSkill(skill),
                                          backgroundColor: AppColors.accent
                                              .withValues(alpha: 0.1),
                                          deleteIconColor: AppColors.accent,
                                        ),
                                      )
                                      .toList(),
                                ),
                        ),

                        const SizedBox(height: 32),

                        // Action buttons
                        Column(
                          children: [
                            // Complete profile button
                            Obx(
                              () => CustomButton(
                                text: 'Complete Profile',
                                onPressed: controller.completeProfileSetup,
                                isLoading: controller.isLoading.value,
                                width: double.infinity,
                              ),
                            ),

                            const SizedBox(height: 12),

                            // Skip button
                            TextButton(
                              onPressed: controller.skipProfileSetup,
                              child: Text(
                                'Skip for now',
                                style: Get.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
