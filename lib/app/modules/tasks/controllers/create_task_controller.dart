import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../data/services/task_service.dart';
import '../../../data/services/project_service.dart';
import '../../../data/services/firebase_storage_service.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/task_model.dart';
import '../../../data/models/user_model.dart';
import '../../../data/models/project_model.dart';
import '../../../core/utils/helpers.dart';

class CreateTaskController extends GetxController {
  static CreateTaskController get instance => Get.find();

  // Services
  final TaskService _taskService = TaskService.instance;
  final ProjectService _projectService = ProjectService.instance;
  final FirebaseStorageService _storageService = FirebaseStorageService.instance;
  final AuthService _authService = AuthService.instance;

  // Form key
  final GlobalKey<FormState> createTaskFormKey = GlobalKey<FormState>();

  // Text editing controllers
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMembers = false.obs;
  final RxString selectedPriority = 'medium'.obs;
  final RxString selectedAssignee = ''.obs;
  final Rxn<File> selectedImage = Rxn<File>();
  final RxList<UserModel> projectMembers = <UserModel>[].obs;

  // Data from arguments
  String? projectId;
  ProjectModel? project;

  // Priority options
  final List<String> priorityOptions = ['low', 'medium', 'high', 'urgent'];

  // Image picker
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    super.onInit();
    _initializeFromArguments();
    _loadProjectMembers();
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  /// Initialize data from navigation arguments
  void _initializeFromArguments() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      projectId = arguments['projectId'] as String?;
      project = arguments['project'] as ProjectModel?;
    }
  }

  /// Load project members for assignee dropdown
  Future<void> _loadProjectMembers() async {
    if (projectId == null) return;

    try {
      isLoadingMembers.value = true;
      final members = await _projectService.getProjectMembers(projectId!);
      projectMembers.value = members;
      
      if (Get.isLogEnable) {
        Get.log('Loaded ${members.length} project members');
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to load project members: $e',
        isError: true,
      );
    } finally {
      isLoadingMembers.value = false;
    }
  }

  /// Pick image from gallery
  Future<void> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        selectedImage.value = File(image.path);
        Helpers.showSnackbar(
          message: 'Image selected successfully',
          isError: false,
        );
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to pick image: $e',
        isError: true,
      );
    }
  }

  /// Pick image from camera
  Future<void> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        selectedImage.value = File(image.path);
        Helpers.showSnackbar(
          message: 'Image captured successfully',
          isError: false,
        );
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to capture image: $e',
        isError: true,
      );
    }
  }

  /// Remove selected image
  void removeSelectedImage() {
    selectedImage.value = null;
  }

  /// Show image picker options
  void showImagePickerOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Image',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Get.back();
                      pickImageFromCamera();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.camera_alt, size: 40, color: Colors.grey.shade600),
                          const SizedBox(height: 8),
                          Text('Camera', style: Get.textTheme.bodyMedium),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Get.back();
                      pickImageFromGallery();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.photo_library, size: 40, color: Colors.grey.shade600),
                          const SizedBox(height: 8),
                          Text('Gallery', style: Get.textTheme.bodyMedium),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Change priority
  void changePriority(String priority) {
    selectedPriority.value = priority;
  }

  /// Change assignee
  void changeAssignee(String userId) {
    selectedAssignee.value = userId;
  }

  /// Get priority display name
  String getPriorityDisplayName(String priority) {
    switch (priority) {
      case 'low':
        return 'Low';
      case 'medium':
        return 'Medium';
      case 'high':
        return 'High';
      case 'urgent':
        return 'Urgent';
      default:
        return priority;
    }
  }

  /// Validate form
  bool _validateForm() {
    if (!createTaskFormKey.currentState!.validate()) {
      return false;
    }

    if (projectId == null) {
      Helpers.showSnackbar(
        message: 'Project not found',
        isError: true,
      );
      return false;
    }

    return true;
  }

  /// Create task
  Future<void> createTask() async {
    if (!_validateForm()) return;

    try {
      isLoading.value = true;

      String? imageUrl;
      
      // Upload image if selected
      if (selectedImage.value != null) {
        imageUrl = await _storageService.uploadTaskImage(
          selectedImage.value!,
          projectId!,
        );
      }

      // Create task
      final task = await _taskService.createTask(
        projectId: projectId!,
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        priority: TaskPriority.fromString(selectedPriority.value),
        assignedTo: selectedAssignee.value.isNotEmpty ? selectedAssignee.value : null,
        imageUrl: imageUrl,
      );

      Helpers.showSnackbar(
        message: 'Task "${task.title}" created successfully!',
        isError: false,
      );

      // Navigate back
      Get.back();
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to create task: $e',
        isError: true,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Go back
  void goBack() {
    Get.back();
  }
}
