import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../data/services/auth_service.dart';
import '../../../data/services/project_service.dart';
import '../../../data/models/project_model.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class ProjectsController extends GetxController {
  static ProjectsController get instance => Get.find();

  final AuthService _authService = AuthService.instance;
  final ProjectService _projectService = ProjectService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<ProjectModel> myProjects = <ProjectModel>[].obs;
  final RxList<ProjectModel> joinedProjects = <ProjectModel>[].obs;
  final RxString selectedFilter = 'all'.obs;

  // Stream subscription
  StreamSubscription<List<ProjectModel>>? _projectsSubscription;

  // Filter options
  final List<String> filterOptions = ['all', 'owned', 'joined'];

  // Get current user
  get currentUser => _authService.userModel;

  @override
  void onInit() {
    super.onInit();
    _loadProjects();
  }

  @override
  void onClose() {
    _projectsSubscription?.cancel();
    super.onClose();
  }

  /// Load projects from Firebase
  Future<void> _loadProjects() async {
    try {
      isLoading.value = true;

      // Set up real-time listener for projects
      _projectsSubscription = _projectService.getUserProjectsStream().listen(
        (projects) {
          _updateProjectLists(projects);
          isLoading.value = false;
        },
        onError: (error) {
          Helpers.showSnackbar(
            message: 'Error loading projects: $error',
            isError: true,
          );
          isLoading.value = false;
        },
      );
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to load projects: $e',
        isError: true,
      );
      isLoading.value = false;
    }
  }

  /// Update project lists based on filter
  void _updateProjectLists(List<ProjectModel> allProjects) {
    final currentUserId = _authService.currentUserId;
    if (currentUserId == null) return;

    // Separate owned and joined projects
    final owned = allProjects.where((p) => p.ownerId == currentUserId).toList();
    final joined = allProjects
        .where((p) => p.ownerId != currentUserId)
        .toList();

    myProjects.value = owned;
    joinedProjects.value = joined;
  }

  /// Refresh projects
  Future<void> refreshProjects() async {
    try {
      isLoading.value = true;
      final projects = await _projectService.getUserProjects();
      _updateProjectLists(projects);
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to refresh projects: $e',
        isError: true,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Navigate to create project
  void createProject() {
    Get.toNamed(AppRoutes.createProject);
  }

  /// Get filtered projects based on selected filter
  List<ProjectModel> get filteredProjects {
    switch (selectedFilter.value) {
      case 'owned':
        return myProjects;
      case 'joined':
        return joinedProjects;
      default:
        return [...myProjects, ...joinedProjects];
    }
  }

  /// Change filter
  void changeFilter(String filter) {
    selectedFilter.value = filter;
  }

  /// Navigate to project details
  void openProject(ProjectModel project) {
    Get.toNamed(AppRoutes.projectDetails, arguments: project.id);
  }

  /// Delete project (only for owners)
  Future<void> deleteProject(ProjectModel project) async {
    try {
      final currentUserId = _authService.currentUserId;
      if (currentUserId == null || project.ownerId != currentUserId) {
        Helpers.showSnackbar(
          message: 'You can only delete projects you own',
          isError: true,
        );
        return;
      }

      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('Delete Project'),
          content: Text(
            'Are you sure you want to delete "${project.name}"? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _projectService.deleteProject(project.id);
        Helpers.showSnackbar(
          message: 'Project "${project.name}" deleted successfully',
          isError: false,
        );
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to delete project: $e',
        isError: true,
      );
    }
  }
}
