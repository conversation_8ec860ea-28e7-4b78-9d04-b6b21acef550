import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import '../services/auth_service.dart';
import '../../core/constants/app_constants.dart';

class FirebaseStorageService extends GetxService {
  static FirebaseStorageService get instance => Get.find();

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final AuthService _authService = AuthService.instance;

  /// Upload task image/screenshot
  Future<String?> uploadTaskImage(File imageFile, String projectId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = 'task_${timestamp}_${currentUser.uid}$extension';
      
      // Create storage reference
      final storageRef = _storage
          .ref()
          .child(AppConstants.taskScreenshotsPath)
          .child(projectId)
          .child(fileName);

      // Upload file
      final uploadTask = storageRef.putFile(imageFile);
      
      // Monitor upload progress (optional)
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        if (Get.isLogEnable) {
          Get.log('Upload progress: ${(progress * 100).toStringAsFixed(2)}%');
        }
      });

      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      if (Get.isLogEnable) {
        Get.log('Task image uploaded successfully: $downloadUrl');
      }
      
      return downloadUrl;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error uploading task image: $e');
      }
      rethrow;
    }
  }

  /// Delete task image
  Future<void> deleteTaskImage(String imageUrl) async {
    try {
      if (imageUrl.isEmpty) return;
      
      // Get reference from URL
      final ref = _storage.refFromURL(imageUrl);
      
      // Delete file
      await ref.delete();
      
      if (Get.isLogEnable) {
        Get.log('Task image deleted successfully: $imageUrl');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error deleting task image: $e');
      }
      // Don't rethrow for delete operations as it's not critical
    }
  }

  /// Upload profile image
  Future<String?> uploadProfileImage(File imageFile, String userId) async {
    try {
      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = 'profile_${timestamp}_$userId$extension';
      
      // Create storage reference
      final storageRef = _storage
          .ref()
          .child(AppConstants.profileImagesPath)
          .child(fileName);

      // Upload file
      final uploadTask = storageRef.putFile(imageFile);
      
      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      if (Get.isLogEnable) {
        Get.log('Profile image uploaded successfully: $downloadUrl');
      }
      
      return downloadUrl;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error uploading profile image: $e');
      }
      rethrow;
    }
  }

  /// Delete profile image
  Future<void> deleteProfileImage(String imageUrl) async {
    try {
      if (imageUrl.isEmpty) return;
      
      // Get reference from URL
      final ref = _storage.refFromURL(imageUrl);
      
      // Delete file
      await ref.delete();
      
      if (Get.isLogEnable) {
        Get.log('Profile image deleted successfully: $imageUrl');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error deleting profile image: $e');
      }
      // Don't rethrow for delete operations as it's not critical
    }
  }

  /// Get file size in bytes
  Future<int?> getFileSize(String fileUrl) async {
    try {
      final ref = _storage.refFromURL(fileUrl);
      final metadata = await ref.getMetadata();
      return metadata.size;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error getting file size: $e');
      }
      return null;
    }
  }

  /// Check if file exists
  Future<bool> fileExists(String fileUrl) async {
    try {
      final ref = _storage.refFromURL(fileUrl);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }
}
