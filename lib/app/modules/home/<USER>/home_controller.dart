import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';

class HomeController extends GetxController {
  static HomeController get instance => Get.find();

  final AuthService _authService = AuthService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList recentProjects = [].obs;
  final RxList recentTasks = [].obs;

  // Get current user
  get currentUser => _authService.userModel;

  @override
  void onInit() {
    super.onInit();
    _loadDashboardData();
  }

  // Load dashboard data
  Future<void> _loadDashboardData() async {
    try {
      isLoading.value = true;

      // TODO: Load recent projects and tasks
      // This will be implemented when we create the project and task services

      await Future.delayed(const Duration(seconds: 1)); // Simulate loading
    } catch (e) {
      Get.snackbar('Error', 'Failed to load dashboard data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh dashboard
  Future<void> refreshDashboard() async {
    await _loadDashboardData();
  }
}
