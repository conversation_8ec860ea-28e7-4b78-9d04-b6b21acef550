import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class SignupController extends GetxController {
  static SignupController get instance => Get.find();

  // Form key
  final GlobalKey<FormState> signupFormKey = GlobalKey<FormState>();

  // Text editing controllers
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  // Observable variables
  // Loading state from AuthService
  bool get isLoading => _authService.isLoading;
  final RxBool isPasswordVisible = false.obs;
  final RxBool isConfirmPasswordVisible = false.obs;
  final RxBool acceptTerms = false.obs;

  // Auth service
  final AuthService _authService = AuthService.instance;

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  // Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  // Toggle confirm password visibility
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  // Toggle accept terms
  void toggleAcceptTerms() {
    acceptTerms.value = !acceptTerms.value;
  }

  // Validate form
  bool _validateForm() {
    if (!acceptTerms.value) {
      Helpers.showSnackbar(
        message: 'Please accept the terms and conditions',
        isError: true,
      );
      return false;
    }
    return signupFormKey.currentState?.validate() ?? false;
  }

  // Sign up with email and password
  Future<void> signupWithEmailAndPassword() async {
    if (!_validateForm()) return;

    try {
      final user = await _authService.signUpWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text,
        name: nameController.text.trim(),
      );

      if (user != null) {
        Helpers.showSnackbar(
          message: 'Account created successfully! Welcome, ${user.name}!',
          isError: false,
        );

        // Navigate to profile setup for new users
        Get.offAllNamed(AppRoutes.profileSetup);
      }
    } catch (e) {
      Helpers.showSnackbar(message: e.toString(), isError: true);
    }
  }

  // Navigate to login
  void goToLogin() {
    Get.back();
  }

  // Validate name
  String? validateName(String? value) {
    return Validators.validateName(value);
  }

  // Validate email
  String? validateEmail(String? value) {
    return Validators.validateEmail(value);
  }

  // Validate password
  String? validatePassword(String? value) {
    return Validators.validatePassword(value);
  }

  // Validate confirm password
  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != passwordController.text) {
      return 'Passwords do not match';
    }

    return null;
  }

  // Clear form
  void clearForm() {
    nameController.clear();
    emailController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    isPasswordVisible.value = false;
    isConfirmPasswordVisible.value = false;
    acceptTerms.value = false;
  }
}
