#!/bin/bash

echo "🔧 Fixing Android Storage Issues..."

# 1. Clean Flutter build cache
echo "1. Cleaning Flutter build cache..."
flutter clean

# 2. Clean Android build cache
echo "2. Cleaning Android build cache..."
cd android
./gradlew clean
cd ..

# 3. Clean pub cache
echo "3. Cleaning pub cache..."
flutter pub cache clean

# 4. Get dependencies
echo "4. Getting dependencies..."
flutter pub get

# 5. Instructions for emulator storage
echo ""
echo "📱 To fix Android Emulator storage issues:"
echo "1. Open Android Studio"
echo "2. Go to Tools > AVD Manager"
echo "3. Click the pencil icon next to your emulator"
echo "4. Click 'Show Advanced Settings'"
echo "5. Increase 'Internal Storage' to at least 8GB"
echo "6. Click 'Finish' and restart the emulator"
echo ""
echo "Or run: flutter run -d chrome --web-port 53925 (to use web instead)"
echo ""
echo "✅ Cleanup complete!"
