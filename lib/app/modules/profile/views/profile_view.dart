import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/helpers.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile header
            _buildProfileHeader(),
            
            const SizedBox(height: 32),
            
            // Profile options
            _buildProfileOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Obx(() {
      final user = controller.currentUser;
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(16),
        ),
        child: <PERSON>umn(
          children: [
            // Profile avatar
            CircleAvatar(
              radius: 50,
              backgroundColor: AppColors.surface,
              child: user?.profileImage != null
                  ? ClipOval(
                      child: Image.network(
                        user!.profileImage!,
                        width: 100,
                        height: 100,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildAvatarFallback(user.name);
                        },
                      ),
                    )
                  : _buildAvatarFallback(user?.name ?? 'User'),
            ),
            
            const SizedBox(height: 16),
            
            // User name
            Text(
              user?.name ?? 'User',
              style: Get.textTheme.headlineMedium?.copyWith(
                color: AppColors.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 4),
            
            // User email
            Text(
              user?.email ?? '<EMAIL>',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.onPrimary.withValues(alpha: 0.8),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Edit profile button
            OutlinedButton(
              onPressed: controller.editProfile,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.onPrimary,
                side: const BorderSide(color: AppColors.onPrimary),
              ),
              child: const Text('Edit Profile'),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildAvatarFallback(String name) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: Helpers.getRandomColor(name),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          Helpers.getInitials(name),
          style: Get.textTheme.headlineMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileOptions() {
    return Column(
      children: [
        _buildOptionTile(
          icon: Icons.settings_outlined,
          title: 'Settings',
          subtitle: 'App preferences and configurations',
          onTap: controller.openSettings,
        ),
        
        _buildOptionTile(
          icon: Icons.help_outline,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onTap: () {
            Get.snackbar('Info', 'Help & Support feature coming soon!');
          },
        ),
        
        _buildOptionTile(
          icon: Icons.info_outline,
          title: 'About',
          subtitle: 'App version and information',
          onTap: controller.showAbout,
        ),
        
        const SizedBox(height: 16),
        
        _buildOptionTile(
          icon: Icons.logout,
          title: 'Logout',
          subtitle: 'Sign out of your account',
          onTap: controller.logout,
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.onSurface,
        ),
        title: Text(
          title,
          style: Get.textTheme.titleMedium?.copyWith(
            color: isDestructive ? AppColors.error : AppColors.onSurface,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Get.textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: AppColors.onSurfaceVariant,
        ),
        onTap: onTap,
      ),
    );
  }
}
