{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987be6064809e7ee39e979675a884f0c77", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985b0c20c3f1f32c3d228e924cdb1cd778", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980abc2037a82109f1bb4549da16791f0f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989233d055ff1706075005adec68b60609", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980abc2037a82109f1bb4549da16791f0f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd2dfff5d03c4fddc791d4de8d0aafb0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ab741af90e2e565c58f236cf0404993d", "guid": "bfdfe7dc352907fc980b868725387e986b83463a7618fc8cf74d0350d4b6a336", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e403ea56f1061b0b776559690499685", "guid": "bfdfe7dc352907fc980b868725387e98b3fdc151afae6deac0598c32da23cc79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f06fb69995cf3c08d3fefc2c48932aeb", "guid": "bfdfe7dc352907fc980b868725387e98b2d2d2a4213f0a2cc8c4adad9b521603", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384935c7dbc631af57b75df6caaf40dc", "guid": "bfdfe7dc352907fc980b868725387e98bb059bbbbb4154a965749f225a8b9b77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893693b350f1ff0c250ff61c579e7f552", "guid": "bfdfe7dc352907fc980b868725387e98cfd48927bbc99a5997eb90d81e58faae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b996843b766dd419531955a10ff2d608", "guid": "bfdfe7dc352907fc980b868725387e985ca63286043462a531c83c847cb887a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ee3857b25ef4c2794ca19d48f73867", "guid": "bfdfe7dc352907fc980b868725387e98c2449377863c53bc6db7b15b6a7db6ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0400020b5bb29f6b239706deb461e18", "guid": "bfdfe7dc352907fc980b868725387e985025a9ea751050ffec534b1daa8dcf95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d393fc780fd14ea7d73dc5c68d88a7", "guid": "bfdfe7dc352907fc980b868725387e98ae5576618a01fd617d579342c0285e42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cc8a103cefe802e453528860d6184a6", "guid": "bfdfe7dc352907fc980b868725387e988880a09eb2f5bae882c2bbb2c70a6702", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858ac25f282ea384ccba15f5282d39186", "guid": "bfdfe7dc352907fc980b868725387e98896348a88c12d448fc3ae8aadeb8a7e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82a4ed4760124323688c73d3a007f17", "guid": "bfdfe7dc352907fc980b868725387e986dc584a2f840014b392acc43656a80c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e43af6933a9213826106a0460d6e43c", "guid": "bfdfe7dc352907fc980b868725387e98e0b34dc29726fbaa243ef5d7e3468589", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5443396903374e098e34b4aef2fb34e", "guid": "bfdfe7dc352907fc980b868725387e983761070849c7db9ce55df82ab35d7944", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826473fec8e15e572a775ba29072fb3bb", "guid": "bfdfe7dc352907fc980b868725387e98040b991f880a062f6b8db38a909ce783", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c462b482b619fb1beb2d2f80051697b", "guid": "bfdfe7dc352907fc980b868725387e98a9c5ecfc860540d868da5eb301a59258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982225abe7138cfd15b536f546541329b0", "guid": "bfdfe7dc352907fc980b868725387e983da3a9c0e999e3dce211cdef86c12bbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa939edb9549e5319c3548cc1487a576", "guid": "bfdfe7dc352907fc980b868725387e98dd27477ca0bf88f45db2b3542b969d62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3eb1393df8b47be8399d5d46a54c68", "guid": "bfdfe7dc352907fc980b868725387e986af4da2a625d5fc11df1c4449698a372", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986808c0234fb2524f8e8e4771f8cd6786", "guid": "bfdfe7dc352907fc980b868725387e9856a3eb55dd74d2703524986801d7775c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e3cff4146c05ea300d314c0ddb95e3", "guid": "bfdfe7dc352907fc980b868725387e9867616012378fdd2e33dfd48c2b8afec8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833442c100d8b60f3513edf15dc99fb9f", "guid": "bfdfe7dc352907fc980b868725387e989632ea27f21466918488e8ef8276a534", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983ed81830db4d9893da1652f239717b7b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988f0e4659ff5026e57fb26fd10b21da21", "guid": "bfdfe7dc352907fc980b868725387e982cae55ed41aa9bd6cb2c5d1170f543af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4a5d959e1aa46ae92e0712a89c6628", "guid": "bfdfe7dc352907fc980b868725387e984678bf84a2b93cc35ddcb89cbea35bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f206adfd406cea301a3ee4cceb3f7b0", "guid": "bfdfe7dc352907fc980b868725387e981daf61a60b922eca02e1bc88bd11b039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356b99ce5bfd8722da5167c083bfad1b", "guid": "bfdfe7dc352907fc980b868725387e984bb91e31ae8deb9d68ba4b0156a8294d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d665e07ac5f67eb9659bbadb98d1dc", "guid": "bfdfe7dc352907fc980b868725387e980068344a482c16f51673de0c9c93c196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4df114c611b443875944d45b9f68032", "guid": "bfdfe7dc352907fc980b868725387e985598aa38b0f38bbacd6535d5f115620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a43b070c460719dc510160eaf2428684", "guid": "bfdfe7dc352907fc980b868725387e98545bd73e286a84311f367681f2598dee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ff9edea4ecd1656c5becd0ad8af6dff", "guid": "bfdfe7dc352907fc980b868725387e98c6d69c28ddf3fc9186a4e9d05977ba38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ab89e6c994a5a71ba564f3d1b85692c", "guid": "bfdfe7dc352907fc980b868725387e981c65a454f7587d9b3c35a293c1f34358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd083c32b45bcb2d1d31684bdd9f8dc9", "guid": "bfdfe7dc352907fc980b868725387e98124ce10c645406f41e5b4c3c04974b34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98216a485e81f31381a109892b3dd5af97", "guid": "bfdfe7dc352907fc980b868725387e98f52ff329c631b58e422d5552b0173340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ffb4c9a3192ff04da52c90bf5791a2", "guid": "bfdfe7dc352907fc980b868725387e985edaf89fa2840edac77d2bb20916b86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6845b832ca3097802bcacb6ef3e836", "guid": "bfdfe7dc352907fc980b868725387e98ed3f78781cce3ad0dcbf3b15fe7b4c46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98898d654e6bc1407a73dc752d5acc8094", "guid": "bfdfe7dc352907fc980b868725387e987e7fc5529e8a9bda02db5341b4a4a20e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887f799e1743717e3239eff40159aa77c", "guid": "bfdfe7dc352907fc980b868725387e98b589f8e26420321d0b3c0ca8f5dc6ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650a1f7bd349556702c5ddfccaa771e1", "guid": "bfdfe7dc352907fc980b868725387e981ff5c59f5f57fa64d6070fb591156e51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d7377bbe42caaa6ef2979d7040812e", "guid": "bfdfe7dc352907fc980b868725387e98ba2cc9f5c5aa1ffda615432753bafe12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b2c549ff8d09e9ff03ce8567540816d", "guid": "bfdfe7dc352907fc980b868725387e98f2d34692abd240a8d6fb9e67839e0212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883f5f621ca9cb71e1e62064fbfc575ba", "guid": "bfdfe7dc352907fc980b868725387e983737385c63fd4454f7f487be5b36bacf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884539b569f0c7e4b38558fdafb8385e6", "guid": "bfdfe7dc352907fc980b868725387e987ab3dbb461fcf998fd94b656b884a6bb"}], "guid": "bfdfe7dc352907fc980b868725387e983959574dbca87e8a7fd88fb9926d6e9b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9841473a97441b203ad5bd4e12c57c8930"}], "guid": "bfdfe7dc352907fc980b868725387e981b0d44e8a9ebec36768a68beb8ce049c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984ed3b4a52ff808574a9df9f8ef201272", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e987e327aac26a5c8d6d44dbd0bad7646f8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}