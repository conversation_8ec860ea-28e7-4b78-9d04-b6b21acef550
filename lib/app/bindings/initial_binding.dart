import 'package:get/get.dart';
import '../data/services/auth_service.dart';
import '../data/services/firebase_service.dart';
import '../data/services/storage_service.dart';
import '../data/services/firebase_storage_service.dart';
import '../data/services/project_service.dart';
import '../data/services/task_service.dart';
import '../data/services/join_request_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize core services in order
    Get.put<StorageService>(StorageService(), permanent: true);
    Get.put<FirebaseService>(FirebaseService(), permanent: true);
    Get.put<FirebaseStorageService>(FirebaseStorageService(), permanent: true);
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<ProjectService>(ProjectService(), permanent: true);
    Get.put<TaskService>(TaskService(), permanent: true);
    Get.put<JoinRequestService>(JoinRequestService(), permanent: true);
  }
}
