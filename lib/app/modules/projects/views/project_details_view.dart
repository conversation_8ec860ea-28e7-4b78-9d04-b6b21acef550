import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../controllers/project_details_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../data/models/task_model.dart';

class ProjectDetailsView extends GetView<ProjectDetailsController> {
  const ProjectDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Obx(
          () => Text(controller.project.value?.name ?? 'Project Details'),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.onSurface,
        actions: [
          Obx(() {
            if (controller.canManageProject) {
              return PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'settings':
                      controller.openProjectSettings();
                      break;
                    case 'members':
                      // TODO: Navigate to members page
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'settings',
                    child: Text('Project Settings'),
                  ),
                  const PopupMenuItem(
                    value: 'members',
                    child: Text('Manage Members'),
                  ),
                ],
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.project.value == null) {
          return const Center(child: Text('Project not found'));
        }

        return RefreshIndicator(
          onRefresh: controller.refreshProject,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProjectHeader(),
                const SizedBox(height: 24),
                _buildProjectStats(),
                const SizedBox(height: 24),
                _buildTasksSection(),
                const SizedBox(height: 24),
                if (controller.canManageProject) ...[
                  _buildJoinRequestsSection(),
                  const SizedBox(height: 24),
                ],
              ],
            ),
          ),
        );
      }),
      floatingActionButton: Obx(() {
        if (controller.project.value != null) {
          return FloatingActionButton.extended(
            onPressed: controller.createTask,
            icon: const Icon(Icons.add),
            label: const Text('Add Task'),
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.onPrimary,
          );
        }
        return const SizedBox.shrink();
      }),
    );
  }

  Widget _buildProjectHeader() {
    return Obx(() {
      final project = controller.project.value!;
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.onSurface.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    project.name,
                    style: Get.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: project.isPublic
                        ? AppColors.accent
                        : AppColors.secondary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    project.isPublic ? 'Public' : 'Private',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            if (project.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                project.description,
                style: Get.textTheme.bodyLarge?.copyWith(
                  color: AppColors.onSurface.withOpacity(0.7),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.people_outline,
                  size: 20,
                  color: AppColors.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  '${project.totalMembers} members',
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: AppColors.onSurface.withOpacity(0.6),
                  ),
                ),
                const SizedBox(width: 24),
                Icon(
                  Icons.calendar_today_outlined,
                  size: 20,
                  color: AppColors.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  'Created ${_formatDate(project.createdAt)}',
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: AppColors.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildProjectStats() {
    return Obx(() {
      final stats = controller.taskStats;
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Project Progress',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Tasks',
                    stats['total'].toString(),
                    Icons.assignment_outlined,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'In Progress',
                    stats['inProgress'].toString(),
                    Icons.hourglass_empty,
                    AppColors.accent,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'To Do',
                    stats['todo'].toString(),
                    Icons.radio_button_unchecked,
                    AppColors.secondary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Completed',
                    stats['completed'].toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Get.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Get.textTheme.bodySmall?.copyWith(
              color: AppColors.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTasksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Tasks',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
            const Spacer(),
            Obx(
              () => DropdownButton<String>(
                value: controller.selectedTaskFilter.value,
                onChanged: (value) {
                  if (value != null) {
                    controller.changeTaskFilter(value);
                  }
                },
                items: controller.taskFilterOptions.map((filter) {
                  return DropdownMenuItem(
                    value: filter,
                    child: Text(_getFilterDisplayName(filter)),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Obx(() {
          if (controller.isTasksLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          final filteredTasks = controller.filteredTasks;

          if (filteredTasks.isEmpty) {
            return _buildEmptyTasksState();
          }

          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: filteredTasks.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final task = filteredTasks[index];
              return _buildTaskCard(task);
            },
          );
        }),
      ],
    );
  }

  Widget _buildTaskCard(TaskModel task) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.onSurface.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  task.title,
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    decoration: task.isCompleted
                        ? TextDecoration.lineThrough
                        : null,
                  ),
                ),
              ),
              _buildTaskStatusChip(task.status),
            ],
          ),
          if (task.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              task.description,
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurface.withOpacity(0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: 12),
          Row(
            children: [
              _buildTaskPriorityChip(task.priority),
              const SizedBox(width: 12),
              if (task.hasDueDate) ...[
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: task.isOverdue
                      ? Colors.red
                      : AppColors.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(task.dueDate!),
                  style: Get.textTheme.bodySmall?.copyWith(
                    color: task.isOverdue
                        ? Colors.red
                        : AppColors.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
              const Spacer(),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      // TODO: Navigate to edit task
                      break;
                    case 'delete':
                      controller.deleteTask(task);
                      break;
                    case 'todo':
                      controller.updateTaskStatus(task, TaskStatus.todo);
                      break;
                    case 'in_progress':
                      controller.updateTaskStatus(task, TaskStatus.inProgress);
                      break;
                    case 'completed':
                      controller.updateTaskStatus(task, TaskStatus.completed);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('Edit')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'todo',
                    child: Text('Mark as To Do'),
                  ),
                  const PopupMenuItem(
                    value: 'in_progress',
                    child: Text('Mark as In Progress'),
                  ),
                  const PopupMenuItem(
                    value: 'completed',
                    child: Text('Mark as Completed'),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaskStatusChip(TaskStatus status) {
    Color color;
    switch (status) {
      case TaskStatus.todo:
        color = AppColors.secondary;
        break;
      case TaskStatus.inProgress:
        color = AppColors.accent;
        break;
      case TaskStatus.completed:
        color = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: Get.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTaskPriorityChip(TaskPriority priority) {
    Color color;
    switch (priority) {
      case TaskPriority.low:
        color = Colors.blue;
        break;
      case TaskPriority.medium:
        color = Colors.orange;
        break;
      case TaskPriority.high:
        color = Colors.red;
        break;
      case TaskPriority.urgent:
        color = Colors.purple;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        priority.displayName,
        style: Get.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEmptyTasksState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: AppColors.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No tasks yet',
            style: Get.textTheme.titleLarge?.copyWith(
              color: AppColors.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first task to get started',
            style: Get.textTheme.bodyMedium?.copyWith(
              color: AppColors.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildJoinRequestsSection() {
    return Obx(() {
      if (controller.joinRequests.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Join Requests (${controller.joinRequests.length})',
            style: Get.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.joinRequests.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final request = controller.joinRequests[index];
              return _buildJoinRequestCard(request);
            },
          ),
        ],
      );
    });
  }

  Widget _buildJoinRequestCard(request) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.accent.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      request.userName,
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      request.userEmail,
                      style: Get.textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                _formatDate(request.requestedAt),
                style: Get.textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
          if (request.message.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              request.message,
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurface.withOpacity(0.8),
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Approve',
                  onPressed: () => controller.approveJoinRequest(request),
                  type: ButtonType.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomButton(
                  text: 'Reject',
                  onPressed: () => controller.rejectJoinRequest(request),
                  type: ButtonType.outline,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'all':
        return 'All Tasks';
      case 'todo':
        return 'To Do';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return filter;
    }
  }
}
