class AppConstants {
  // App Information
  static const String appName = 'Project Management';
  static const String appVersion = '1.0.0';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String projectsCollection = 'projects';
  static const String tasksCollection = 'tasks';
  static const String joinRequestsCollection = 'join_requests';
  
  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String taskScreenshotsPath = 'task_screenshots';
  
  // Validation Constants
  static const int minPasswordLength = 6;
  static const int maxProjectNameLength = 50;
  static const int maxProjectDescriptionLength = 500;
  static const int maxTaskTitleLength = 100;
  static const int maxTaskDescriptionLength = 1000;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Error Messages
  static const String networkError = 'Network error. Please check your connection.';
  static const String unknownError = 'An unknown error occurred. Please try again.';
  static const String authError = 'Authentication failed. Please try again.';
  static const String permissionError = 'You don\'t have permission to perform this action.';
  
  // Success Messages
  static const String loginSuccess = 'Login successful!';
  static const String signupSuccess = 'Account created successfully!';
  static const String projectCreatedSuccess = 'Project created successfully!';
  static const String taskCreatedSuccess = 'Task created successfully!';
  static const String joinRequestSentSuccess = 'Join request sent successfully!';
  
  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  static const String nameRequired = 'Name is required';
  static const String projectNameRequired = 'Project name is required';
  static const String projectDescriptionRequired = 'Project description is required';
  static const String taskTitleRequired = 'Task title is required';
  static const String taskDescriptionRequired = 'Task description is required';
  
  // Role Constants
  static const String roleOwner = 'owner';
  static const String roleManager = 'manager';
  static const String roleDeveloper = 'developer';
  
  // Task Status Constants
  static const String taskStatusPending = 'pending';
  static const String taskStatusInProgress = 'in_progress';
  static const String taskStatusCompleted = 'completed';
  
  // Join Request Status Constants
  static const String joinRequestStatusPending = 'pending';
  static const String joinRequestStatusAccepted = 'accepted';
  static const String joinRequestStatusRejected = 'rejected';
  
  // Image Constants
  static const int maxImageSizeInBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png'];
  
  // Pagination Constants
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
}
