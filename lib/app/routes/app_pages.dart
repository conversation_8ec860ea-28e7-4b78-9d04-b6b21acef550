import 'package:get/get.dart';
import 'app_routes.dart';
import '../modules/auth/bindings/auth_binding.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/auth/views/signup_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/main/bindings/main_binding.dart';
import '../modules/main/views/main_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_setup_view.dart';
import '../modules/projects/bindings/projects_binding.dart';
import '../modules/projects/views/create_project_view.dart';
import '../modules/projects/views/project_details_view.dart';
import '../modules/projects/views/discover_projects_view.dart';
import '../modules/projects/controllers/create_project_controller.dart';
import '../modules/projects/controllers/project_details_controller.dart';
import '../modules/projects/controllers/discover_projects_controller.dart';
import '../modules/tasks/views/create_task_view.dart';
import '../modules/tasks/controllers/create_task_controller.dart';

class AppPages {
  AppPages._();

  static final routes = [
    // Splash Route
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashView(),
      binding: SplashBinding(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // Authentication Routes
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginView(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: AppRoutes.signup,
      page: () => const SignupView(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // Main App Routes
    GetPage(
      name: AppRoutes.main,
      page: () => const MainView(),
      binding: MainBinding(),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // Profile Routes
    GetPage(
      name: AppRoutes.profileSetup,
      page: () => const ProfileSetupView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // Project Routes
    GetPage(
      name: AppRoutes.createProject,
      page: () => const CreateProjectView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<CreateProjectController>(() => CreateProjectController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: AppRoutes.projectDetails,
      page: () => const ProjectDetailsView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<ProjectDetailsController>(() => ProjectDetailsController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    GetPage(
      name: AppRoutes.discoverProjects,
      page: () => const DiscoverProjectsView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<DiscoverProjectsController>(
          () => DiscoverProjectsController(),
        );
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // Task Routes
    GetPage(
      name: AppRoutes.createTask,
      page: () => const CreateTaskView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<CreateTaskController>(() => CreateTaskController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];
}
