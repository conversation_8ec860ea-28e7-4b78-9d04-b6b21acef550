// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCzWF42JCuUnmaNQowr99k6P-l9Ufw5iCk',
    appId:
        '1:134000387038:web:YOUR_WEB_APP_ID', // You need to add a web app in Firebase Console
    messagingSenderId: '134000387038',
    projectId: 'atif-firdos-business',
    authDomain: 'atif-firdos-business.firebaseapp.com',
    storageBucket: 'atif-firdos-business.appspot.com',
    measurementId: 'G-MEASUREMENT_ID', // Optional: Add from Firebase Console
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCzWF42JCuUnmaNQowr99k6P-l9Ufw5iCk',
    appId: '1:134000387038:android:42984af760132c9f8b734f',
    messagingSenderId: '134000387038',
    projectId: 'atif-firdos-business',
    storageBucket: 'atif-firdos-business.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey:
        'AIzaSyBqJVJKvwqGtbovXz8Q2YzQzQzQzQzQzQz', // Replace with your iOS API Key
    appId:
        '1:123456789012:ios:abcdef1234567890abcdef', // Replace with your iOS App ID
    messagingSenderId: '123456789012', // Replace with your Sender ID
    projectId: 'project-management-prod', // Replace with your Project ID
    storageBucket:
        'project-management-prod.appspot.com', // Replace with your Storage Bucket
    iosBundleId:
        'com.yourcompany.projectmanagement', // Replace with your iOS Bundle ID
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey:
        'AIzaSyBqJVJKvwqGtbovXz8Q2YzQzQzQzQzQzQz', // Replace with your macOS API Key
    appId:
        '1:123456789012:macos:abcdef1234567890abcdef', // Replace with your macOS App ID
    messagingSenderId: '123456789012', // Replace with your Sender ID
    projectId: 'project-management-prod', // Replace with your Project ID
    storageBucket:
        'project-management-prod.appspot.com', // Replace with your Storage Bucket
    iosBundleId:
        'com.yourcompany.projectmanagement', // Replace with your macOS Bundle ID
  );
}
