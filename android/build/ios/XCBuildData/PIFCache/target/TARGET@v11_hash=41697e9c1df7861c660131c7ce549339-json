{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98af0c2e634319b5d3d611d98aea77e0e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982bfd95f1f48ca6b1348a66db18d21287", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c0a6f8dfc20150ee462b6ecb2881f3d4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5219d7330cf2e66b18607e2989676e4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c0a6f8dfc20150ee462b6ecb2881f3d4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e842831931c06a0076ce7ac97580821", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984fce52f82c5d8c8426e97e7470f6b97d", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaef421922dbfd7f55d8ab5797a310cc", "guid": "bfdfe7dc352907fc980b868725387e9827d2e3dbe0027e58f9a2211f5ff0ac47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9bcce8d7c3167f5f5e3b1217a19f24e", "guid": "bfdfe7dc352907fc980b868725387e986f31ee89edf9377316226f8bc8dc8819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132e143f49a81cc78f5fb69d3877bbbe", "guid": "bfdfe7dc352907fc980b868725387e9812f2fab375831ed13992076cb61ec98c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986af24a9deffdcb8adb6d7667d7f0c5f6", "guid": "bfdfe7dc352907fc980b868725387e9859d6592f7acad64a8c85641059105825", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987637066d6b0bb7b2610856b3506c0cda", "guid": "bfdfe7dc352907fc980b868725387e985f5d0ba6c68b92a213f62e45bd448b16", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae7bb9dc0251366d4238b5637962e0c", "guid": "bfdfe7dc352907fc980b868725387e980af9d93a068964e2714b1fbf2e65d9fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d819a254ea53fb90889fe04fe384aa96", "guid": "bfdfe7dc352907fc980b868725387e9859380cbb040e61503aaaef652cd2b295", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2dfb68f1aed6ab4f5f7a4bf5c45949", "guid": "bfdfe7dc352907fc980b868725387e9851671094f35d670c2acd44a157487ee0", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c47fe20ba73736934de02c71e0ad30e", "guid": "bfdfe7dc352907fc980b868725387e987954db926fe8ca5daf0d07595f734448", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851ba9d177e7b0af7e8675673d6220322", "guid": "bfdfe7dc352907fc980b868725387e98f87da423adfe4055ef389003860380aa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb0a3425628824bf3b6ecf81fa7ba04", "guid": "bfdfe7dc352907fc980b868725387e98b7c383c11eb8940774c33f1483319a9e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f9b57b2d829192e9715a1dc454255e", "guid": "bfdfe7dc352907fc980b868725387e98598cea28d1e46da27fa6969f85256d9c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea0104802f0b69b339ad8535c9825b7", "guid": "bfdfe7dc352907fc980b868725387e98514db4573ebdba3b29618ffb6e14b623", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9810a051e7c8bb5da36ef9bcc62095c2b6", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862e80a4f46e42b368dd03e835c2bbb9d", "guid": "bfdfe7dc352907fc980b868725387e9871170e0ee0a91b2750e4d748ac2535b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dba93246592a75e0145231d4a5e65c6", "guid": "bfdfe7dc352907fc980b868725387e981ceb5604e5e751d5f01a0496282df68a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953e385b6bfeac92ddb1dbf764fddd64", "guid": "bfdfe7dc352907fc980b868725387e985540fb4fd491b7b1419522fc096e1833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd93b83eb756377ad9b94636d23a05b", "guid": "bfdfe7dc352907fc980b868725387e98e46b926386a21105158de1c559990c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98006d21d9c57b1ebc353827ec22730447", "guid": "bfdfe7dc352907fc980b868725387e989dfc21f3557be62566fb45fce59d2037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc7286a93c98ebdb37d3582a38d36739", "guid": "bfdfe7dc352907fc980b868725387e98e62f3baa439175b4738e52e2513c98fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832425b382975c7dff093b0cdae9f12ef", "guid": "bfdfe7dc352907fc980b868725387e98f809614b8f873046c451838063540256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ca217e1719c214d63932bbe56c9cc6d", "guid": "bfdfe7dc352907fc980b868725387e988453b514331cfc690eeaa87406bb9662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea89b7c3488a58f8ec4f1430bb91a228", "guid": "bfdfe7dc352907fc980b868725387e981da63115fbf5b6a70714ed80ad21d280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd9ea2a44034c691ce6da10b2bffb63", "guid": "bfdfe7dc352907fc980b868725387e98cfb27fa010363d7dfc7a6c7c1830badb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987532bb0dd47b848c152bd307625bc959", "guid": "bfdfe7dc352907fc980b868725387e9812e1f569481009ce3637ccb19c62f781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3206db318388ad9b5e10104d3f1cc4c", "guid": "bfdfe7dc352907fc980b868725387e98dcc11f413d7f9d811855bf768f9caae7"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}