import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/user_model.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class ProfileSetupController extends GetxController {
  static ProfileSetupController get instance => Get.find();

  // Form key
  final GlobalKey<FormState> profileFormKey = GlobalKey<FormState>();

  // Text editing controllers
  final companyNameController = TextEditingController();
  final jobTitleController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final bioController = TextEditingController();
  final skillController = TextEditingController();

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<String> skills = <String>[].obs;

  // Services
  final AuthService _authService = AuthService.instance;

  @override
  void onInit() {
    super.onInit();
    _loadExistingData();
  }

  @override
  void onClose() {
    companyNameController.dispose();
    jobTitleController.dispose();
    phoneNumberController.dispose();
    bioController.dispose();
    skillController.dispose();
    super.onClose();
  }

  // Load existing user data if any
  void _loadExistingData() {
    final user = _authService.userModel;
    if (user != null) {
      companyNameController.text = user.companyName ?? '';
      jobTitleController.text = user.jobTitle ?? '';
      phoneNumberController.text = user.phoneNumber ?? '';
      bioController.text = user.bio ?? '';
      skills.value = List.from(user.skills);
    }
  }

  // Add skill to the list
  void addSkill() {
    final skill = skillController.text.trim();
    if (skill.isNotEmpty && !skills.contains(skill)) {
      skills.add(skill);
      skillController.clear();
    }
  }

  // Remove skill from the list
  void removeSkill(String skill) {
    skills.remove(skill);
  }

  // Validate form
  bool _validateForm() {
    return profileFormKey.currentState?.validate() ?? false;
  }

  // Complete profile setup
  Future<void> completeProfileSetup() async {
    if (!_validateForm()) return;

    try {
      isLoading.value = true;

      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not found. Please login again.';
      }

      // Create updated user model
      final updatedUser = currentUser.copyWith(
        companyName: companyNameController.text.trim(),
        jobTitle: jobTitleController.text.trim(),
        phoneNumber: phoneNumberController.text.trim(),
        bio: bioController.text.trim(),
        skills: skills.toList(),
        isProfileComplete: true,
        updatedAt: DateTime.now(),
      );

      // Update user profile
      await _authService.updateUserProfileFields(
        name: updatedUser.name,
        companyName: updatedUser.companyName,
        jobTitle: updatedUser.jobTitle,
        phoneNumber: updatedUser.phoneNumber,
        bio: updatedUser.bio,
        skills: updatedUser.skills,
        isProfileComplete: true,
      );

      Helpers.showSnackbar(
        message: 'Profile completed successfully!',
        isError: false,
      );

      // Navigate to main app
      Get.offAllNamed(AppRoutes.main);
    } catch (e) {
      Helpers.showSnackbar(message: e.toString(), isError: true);
    } finally {
      isLoading.value = false;
    }
  }

  // Skip profile setup for now
  void skipProfileSetup() {
    Get.offAllNamed(AppRoutes.main);
  }

  // Validate company name
  String? validateCompanyName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Company name is required';
    }
    return null;
  }

  // Validate job title
  String? validateJobTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Job title is required';
    }
    return null;
  }

  // Validate phone number
  String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  // Validate bio
  String? validateBio(String? value) {
    if (value != null && value.length > 500) {
      return 'Bio must be less than 500 characters';
    }
    return null;
  }
}
