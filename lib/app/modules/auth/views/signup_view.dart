import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/signup_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/gradient_background.dart';

class SignupView extends GetView<SignupController> {
  const SignupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Back button
                IconButton(
                  onPressed: controller.goToLogin,
                  icon: const Icon(
                    Icons.arrow_back,
                    color: AppColors.onPrimary,
                  ),
                ),

                const SizedBox(height: 20),

                // Welcome text
                Text(
                  'Create Account',
                  style: Get.textTheme.displayMedium?.copyWith(
                    color: AppColors.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Join us and start managing your projects',
                  style: Get.textTheme.bodyLarge?.copyWith(
                    color: AppColors.onPrimary.withValues(alpha: 0.8),
                  ),
                ),

                const SizedBox(height: 40),

                // Signup form
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowMedium,
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Form(
                    key: controller.signupFormKey,
                    child: Column(
                      children: [
                        // Name field
                        CustomTextField(
                          controller: controller.nameController,
                          labelText: 'Full Name',
                          hintText: 'Enter your full name',
                          keyboardType: TextInputType.name,
                          textCapitalization: TextCapitalization.words,
                          prefixIcon: Icons.person_outlined,
                          validator: controller.validateName,
                        ),

                        const SizedBox(height: 16),

                        // Email field
                        CustomTextField(
                          controller: controller.emailController,
                          labelText: 'Email',
                          hintText: 'Enter your email',
                          keyboardType: TextInputType.emailAddress,
                          prefixIcon: Icons.email_outlined,
                          validator: controller.validateEmail,
                        ),

                        const SizedBox(height: 16),

                        // Password field
                        Obx(
                          () => CustomTextField(
                            controller: controller.passwordController,
                            labelText: 'Password',
                            hintText: 'Enter your password',
                            obscureText: !controller.isPasswordVisible.value,
                            prefixIcon: Icons.lock_outlined,
                            suffixIcon: IconButton(
                              icon: Icon(
                                controller.isPasswordVisible.value
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                              ),
                              onPressed: controller.togglePasswordVisibility,
                            ),
                            validator: controller.validatePassword,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Confirm password field
                        Obx(
                          () => CustomTextField(
                            controller: controller.confirmPasswordController,
                            labelText: 'Confirm Password',
                            hintText: 'Confirm your password',
                            obscureText:
                                !controller.isConfirmPasswordVisible.value,
                            prefixIcon: Icons.lock_outlined,
                            suffixIcon: IconButton(
                              icon: Icon(
                                controller.isConfirmPasswordVisible.value
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                              ),
                              onPressed:
                                  controller.toggleConfirmPasswordVisibility,
                            ),
                            validator: controller.validateConfirmPassword,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Terms and conditions
                        Obx(
                          () => Row(
                            children: [
                              Checkbox(
                                value: controller.acceptTerms.value,
                                onChanged: (_) =>
                                    controller.toggleAcceptTerms(),
                                activeColor: AppColors.accent,
                              ),
                              Expanded(
                                child: RichText(
                                  text: TextSpan(
                                    style: Get.textTheme.bodyMedium,
                                    children: [
                                      const TextSpan(text: 'I agree to the '),
                                      TextSpan(
                                        text: 'Terms and Conditions',
                                        style: Get.textTheme.bodyMedium
                                            ?.copyWith(
                                              color: AppColors.accent,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                      ),
                                      const TextSpan(text: ' and '),
                                      TextSpan(
                                        text: 'Privacy Policy',
                                        style: Get.textTheme.bodyMedium
                                            ?.copyWith(
                                              color: AppColors.accent,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Signup button
                        Obx(
                          () => CustomButton(
                            text: 'Create Account',
                            onPressed: controller.signupWithEmailAndPassword,
                            isLoading: controller.isLoading,
                            width: double.infinity,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Login link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Already have an account? ',
                              style: Get.textTheme.bodyMedium,
                            ),
                            TextButton(
                              onPressed: controller.goToLogin,
                              child: Text(
                                'Sign In',
                                style: Get.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.accent,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
