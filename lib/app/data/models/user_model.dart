import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String email;
  final String name;
  final String? profileImage;
  final String? companyName;
  final String? jobTitle;
  final String? phoneNumber;
  final String? bio;
  final List<String> skills;
  final bool isProfileComplete;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.uid,
    required this.email,
    required this.name,
    this.profileImage,
    this.companyName,
    this.jobTitle,
    this.phoneNumber,
    this.bio,
    this.skills = const [],
    this.isProfileComplete = false,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory constructor to create UserModel from Firestore DocumentSnapshot
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return UserModel(
      uid: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      profileImage: data['profileImage'],
      companyName: data['companyName'],
      jobTitle: data['jobTitle'],
      phoneNumber: data['phoneNumber'],
      bio: data['bio'],
      skills: List<String>.from(data['skills'] ?? []),
      isProfileComplete: data['isProfileComplete'] ?? false,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  // Factory constructor to create UserModel from Map
  factory UserModel.fromMap(Map<String, dynamic> data, String id) {
    return UserModel(
      uid: id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      profileImage: data['profileImage'],
      companyName: data['companyName'],
      jobTitle: data['jobTitle'],
      phoneNumber: data['phoneNumber'],
      bio: data['bio'],
      skills: List<String>.from(data['skills'] ?? []),
      isProfileComplete: data['isProfileComplete'] ?? false,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? DateTime.parse(data['updatedAt'])
          : DateTime.now(),
    );
  }

  // Factory constructor to create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profileImage'],
      companyName: json['companyName'],
      jobTitle: json['jobTitle'],
      phoneNumber: json['phoneNumber'],
      bio: json['bio'],
      skills: List<String>.from(json['skills'] ?? []),
      isProfileComplete: json['isProfileComplete'] ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  // Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'profileImage': profileImage,
      'companyName': companyName,
      'jobTitle': jobTitle,
      'phoneNumber': phoneNumber,
      'bio': bio,
      'skills': skills,
      'isProfileComplete': isProfileComplete,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Convert UserModel to Firestore format (without uid as it's the document ID)
  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'name': name,
      'profileImage': profileImage,
      'companyName': companyName,
      'jobTitle': jobTitle,
      'phoneNumber': phoneNumber,
      'bio': bio,
      'skills': skills,
      'isProfileComplete': isProfileComplete,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Convert UserModel to Map (without uid as it's the document ID)
  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'name': name,
      'profileImage': profileImage,
      'companyName': companyName,
      'jobTitle': jobTitle,
      'phoneNumber': phoneNumber,
      'bio': bio,
      'skills': skills,
      'isProfileComplete': isProfileComplete,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create a copy of UserModel with updated fields
  UserModel copyWith({
    String? uid,
    String? email,
    String? name,
    String? profileImage,
    String? companyName,
    String? jobTitle,
    String? phoneNumber,
    String? bio,
    List<String>? skills,
    bool? isProfileComplete,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
      companyName: companyName ?? this.companyName,
      jobTitle: jobTitle ?? this.jobTitle,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      bio: bio ?? this.bio,
      skills: skills ?? this.skills,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, name: $name, companyName: $companyName, isProfileComplete: $isProfileComplete)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.uid == uid &&
        other.email == email &&
        other.name == name &&
        other.profileImage == profileImage &&
        other.companyName == companyName &&
        other.jobTitle == jobTitle &&
        other.phoneNumber == phoneNumber &&
        other.bio == bio &&
        other.skills.toString() == skills.toString() &&
        other.isProfileComplete == isProfileComplete &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
        email.hashCode ^
        name.hashCode ^
        profileImage.hashCode ^
        companyName.hashCode ^
        jobTitle.hashCode ^
        phoneNumber.hashCode ^
        bio.hashCode ^
        skills.hashCode ^
        isProfileComplete.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
