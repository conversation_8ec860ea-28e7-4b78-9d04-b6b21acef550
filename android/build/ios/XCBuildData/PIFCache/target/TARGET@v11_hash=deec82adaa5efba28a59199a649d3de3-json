{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982936de415e71dfaf44ab2dd0ab97cca0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862ed6de7ae308345c6135c925001766c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da737e412710cae467e269f5134e8a33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fc69c3798b22119b37e00c7dc9e3ecd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da737e412710cae467e269f5134e8a33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98184433a846eee24ea4d3388e65a26b05", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829d67444f39b67842672f2f147d39f06", "guid": "bfdfe7dc352907fc980b868725387e9854905f928f83a03739abc7c29a16d931", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dd8984d542231f64aa14f088a510202d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e5519c3a7580fe239aedfa98eec624d", "guid": "bfdfe7dc352907fc980b868725387e98e077efacdded592884820a30479a82d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008205ece26e6b1ec141af7f844848bb", "guid": "bfdfe7dc352907fc980b868725387e98306402d58fea75da491943bce7800f75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983635e6290dedd2b8bd0196859dcbd29f", "guid": "bfdfe7dc352907fc980b868725387e98359cf161f0af877e7c59a1c83e872315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452540a6365a712ca138938cb513899a", "guid": "bfdfe7dc352907fc980b868725387e98293156b5c244876fb756ac8933ad99b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988baed18b5e418e5c781785c5feb81648", "guid": "bfdfe7dc352907fc980b868725387e981b7d8568dd72eb742ca572c8377ff80e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa601034c6465407f0cf520680ef28d8", "guid": "bfdfe7dc352907fc980b868725387e98d2d351b5131209c1dc13be53ecbf02e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b28cdf382bf75b9310b1e53212e8bb", "guid": "bfdfe7dc352907fc980b868725387e987abaa6c88735972836602b11be2a564f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e752b15365c04197fc019b43968deab1", "guid": "bfdfe7dc352907fc980b868725387e9854d72cb8297519faf6cda3fac26ba068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98920bf257464360db16dc1403482b14c9", "guid": "bfdfe7dc352907fc980b868725387e9876fb2e3c8baa7e445ee7002857e22338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883c06178ce6f6092f43c5c28f3dd1fb1", "guid": "bfdfe7dc352907fc980b868725387e984e42bcbb45f575d376fa596567036d77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b874ba1b26648c0504cda1b9e011c2", "guid": "bfdfe7dc352907fc980b868725387e983506b6abfeff01d1effa1dcab90f0b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69b645f97e339c779c478c6c9b9e4ed", "guid": "bfdfe7dc352907fc980b868725387e98c933b31e48946ced4978c4cdbc75f5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac2b6ebc02985970f9b6d6311e3c16fc", "guid": "bfdfe7dc352907fc980b868725387e989932b920cc1bc250cb1ddef901e80ae6"}], "guid": "bfdfe7dc352907fc980b868725387e988812749a094208a6781ba6d66f766612", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98c9ed2422e2409e69d3a0b7bd9847dcb7"}], "guid": "bfdfe7dc352907fc980b868725387e98d95690c25385e4a7a6fc9e00e9400c20", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c5abc5541b191002c95ad0e06baaea9b", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e985ebdc9dad32942c3610a1d18b9931b3c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}