import '../constants/app_constants.dart';

class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.emailRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AppConstants.emailInvalid;
    }
    
    return null;
  }
  
  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.passwordRequired;
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return AppConstants.passwordTooShort;
    }
    
    return null;
  }
  
  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.nameRequired;
    }
    
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    
    return null;
  }
  
  // Project name validation
  static String? validateProjectName(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.projectNameRequired;
    }
    
    if (value.trim().length < 3) {
      return 'Project name must be at least 3 characters';
    }
    
    if (value.length > AppConstants.maxProjectNameLength) {
      return 'Project name must be less than ${AppConstants.maxProjectNameLength} characters';
    }
    
    return null;
  }
  
  // Project description validation
  static String? validateProjectDescription(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.projectDescriptionRequired;
    }
    
    if (value.trim().length < 10) {
      return 'Project description must be at least 10 characters';
    }
    
    if (value.length > AppConstants.maxProjectDescriptionLength) {
      return 'Project description must be less than ${AppConstants.maxProjectDescriptionLength} characters';
    }
    
    return null;
  }
  
  // Task title validation
  static String? validateTaskTitle(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.taskTitleRequired;
    }
    
    if (value.trim().length < 3) {
      return 'Task title must be at least 3 characters';
    }
    
    if (value.length > AppConstants.maxTaskTitleLength) {
      return 'Task title must be less than ${AppConstants.maxTaskTitleLength} characters';
    }
    
    return null;
  }
  
  // Task description validation
  static String? validateTaskDescription(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.taskDescriptionRequired;
    }
    
    if (value.trim().length < 10) {
      return 'Task description must be at least 10 characters';
    }
    
    if (value.length > AppConstants.maxTaskDescriptionLength) {
      return 'Task description must be less than ${AppConstants.maxTaskDescriptionLength} characters';
    }
    
    return null;
  }
  
  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  // Phone number validation (optional)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }
}
