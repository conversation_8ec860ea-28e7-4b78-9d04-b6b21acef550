import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/services/storage_service.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class LoginController extends GetxController {
  static LoginController get instance => Get.find();

  // Form key
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();

  // Text editing controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // Observable variables
  // Loading state from AuthService
  bool get isLoading => _authService.isLoading;
  final RxBool isPasswordVisible = false.obs;
  final RxBool rememberMe = false.obs;

  // Services
  final AuthService _authService = AuthService.instance;
  final StorageService _storageService = StorageService.instance;

  @override
  void onInit() {
    super.onInit();
    // Load saved email if remember me was checked
    _loadSavedCredentials();
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  // Load saved credentials
  void _loadSavedCredentials() async {
    final remember = await _storageService.rememberMe;
    if (remember) {
      final savedEmail = await _storageService.savedEmail;
      if (savedEmail != null) {
        emailController.text = savedEmail;
        rememberMe.value = true;
      }
    }
  }

  // Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  // Toggle remember me
  void toggleRememberMe() {
    rememberMe.value = !rememberMe.value;
  }

  // Validate form
  bool _validateForm() {
    return loginFormKey.currentState?.validate() ?? false;
  }

  // Login with email and password
  Future<void> loginWithEmailAndPassword() async {
    if (!_validateForm()) return;

    try {
      final user = await _authService.signInWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text,
      );

      if (user != null) {
        // Save credentials if remember me is checked
        await _storageService.saveRememberMe(
          rememberMe.value,
          rememberMe.value ? emailController.text.trim() : null,
        );

        Helpers.showSnackbar(
          message: 'Welcome back, ${user.name}!',
          isError: false,
        );

        // Check if profile is complete
        if (!user.isProfileComplete) {
          // Profile not complete, go to profile setup
          Get.offAllNamed(AppRoutes.profileSetup);
        } else {
          // Navigate to main app
          Get.offAllNamed(AppRoutes.main);
        }
      }
    } catch (e) {
      Helpers.showSnackbar(message: e.toString(), isError: true);
    }
  }

  // Navigate to signup
  void goToSignup() {
    Get.toNamed(AppRoutes.signup);
  }

  // Navigate to forgot password
  void goToForgotPassword() {
    Get.toNamed(AppRoutes.forgotPassword);
  }

  // Validate email
  String? validateEmail(String? value) {
    return Validators.validateEmail(value);
  }

  // Validate password
  String? validatePassword(String? value) {
    return Validators.validatePassword(value);
  }

  // Clear form
  void clearForm() {
    emailController.clear();
    passwordController.clear();
    isPasswordVisible.value = false;
    rememberMe.value = false;
  }
}
