# 🔥 Production Firebase Setup Guide

This guide will help you set up a production-ready Firebase project for the Project Management app.

## 📋 Prerequisites

- Google account
- Flutter development environment
- Firebase CLI installed (`npm install -g firebase-tools`)

## 🚀 Step 1: Create Firebase Project

1. **Go to [Firebase Console](https://console.firebase.google.com)**
2. **Click "Create a project"**
3. **Enter project name**: `project-management-prod` (or your preferred name)
4. **Enable Google Analytics** (recommended for production)
5. **Click "Create project"**

## 🔐 Step 2: Enable Authentication

1. **In Firebase Console, go to "Authentication"**
2. **Click "Get started"**
3. **Go to "Sign-in method" tab**
4. **Enable "Email/Password" provider**
5. **Click "Save"**

## 📊 Step 3: Set up Firestore Database

1. **In Firebase Console, go to "Firestore Database"**
2. **Click "Create database"**
3. **Choose "Start in production mode"** (for security)
4. **Select your preferred location** (choose closest to your users)
5. **Click "Done"**

### Firestore Security Rules (Production-Ready)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Projects - users can only access projects they're members of
    match /projects/{projectId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.members;
      allow create: if request.auth != null;
    }
    
    // Tasks - users can only access tasks in projects they're members of
    match /tasks/{taskId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/projects/$(resource.data.projectId)) &&
        request.auth.uid in get(/databases/$(database)/documents/projects/$(resource.data.projectId)).data.members;
    }
  }
}
```

## 📱 Step 4: Add Flutter App to Firebase

### For Web:
1. **Click "Add app" → Web icon**
2. **Enter app nickname**: `Project Management Web`
3. **Check "Also set up Firebase Hosting"** (optional)
4. **Click "Register app"**
5. **Copy the configuration object**

### For Android:
1. **Click "Add app" → Android icon**
2. **Enter package name**: `com.yourcompany.projectmanagement`
3. **Enter app nickname**: `Project Management Android`
4. **Click "Register app"**
5. **Download `google-services.json`**

### For iOS:
1. **Click "Add app" → iOS icon**
2. **Enter bundle ID**: `com.yourcompany.projectmanagement`
3. **Enter app nickname**: `Project Management iOS`
4. **Click "Register app"**
5. **Download `GoogleService-Info.plist`**

## ⚙️ Step 5: Configure Flutter App

### 1. Install FlutterFire CLI
```bash
dart pub global activate flutterfire_cli
```

### 2. Configure Firebase for Flutter
```bash
flutterfire configure
```

### 3. Replace Firebase Configuration
Replace the content in `lib/firebase_options.dart` with the generated configuration.

### 4. Update Package Name (if needed)
Update `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        applicationId "com.yourcompany.projectmanagement"
        // ... other config
    }
}
```

Update `ios/Runner/Info.plist`:
```xml
<key>CFBundleIdentifier</key>
<string>com.yourcompany.projectmanagement</string>
```

## 🔒 Step 6: Production Security Configuration

### Environment Variables
Create `.env` file (add to `.gitignore`):
```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project.appspot.com
```

### App Check (Recommended for Production)
1. **In Firebase Console, go to "App Check"**
2. **Register your app**
3. **Enable reCAPTCHA for web**
4. **Enable Play Integrity for Android**
5. **Enable App Attest for iOS**

## 📈 Step 7: Enable Additional Services

### Cloud Storage (for file uploads)
1. **Go to "Storage" in Firebase Console**
2. **Click "Get started"**
3. **Choose security rules**
4. **Select storage location**

### Cloud Functions (for backend logic)
```bash
firebase init functions
```

### Analytics (for user insights)
Already enabled if you chose it during project creation.

## 🧪 Step 8: Testing Configuration

### Test Authentication
```dart
// Test sign up
await FirebaseAuth.instance.createUserWithEmailAndPassword(
  email: '<EMAIL>',
  password: 'testpassword123',
);

// Test Firestore
await FirebaseFirestore.instance.collection('test').add({
  'message': 'Hello Firebase!',
  'timestamp': FieldValue.serverTimestamp(),
});
```

## 🚀 Step 9: Deploy to Production

### Web Deployment
```bash
flutter build web
firebase deploy --only hosting
```

### Mobile App Store Deployment
```bash
# Android
flutter build appbundle --release

# iOS
flutter build ios --release
```

## 🔧 Step 10: Monitoring & Maintenance

### Set up Crashlytics
```bash
flutterfire configure
# Select Crashlytics when prompted
```

### Performance Monitoring
Enable in Firebase Console → Performance

### Remote Config
Set up feature flags and app configuration

## 📞 Support

For issues with this setup:
1. Check [Firebase Documentation](https://firebase.google.com/docs)
2. Review [FlutterFire Documentation](https://firebase.flutter.dev)
3. Check Firebase Console for error logs

---

**⚠️ Important Security Notes:**
- Never commit API keys to version control
- Use environment variables for sensitive data
- Regularly review Firestore security rules
- Enable App Check for production apps
- Monitor authentication logs for suspicious activity
