import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'dart:convert';

class StorageService extends GetxService {
  static StorageService get instance => Get.find();

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
  }

  // Ensure prefs is initialized
  Future<SharedPreferences> get prefs async {
    if (_prefs == null) {
      _prefs = await SharedPreferences.getInstance();
    }
    return _prefs!;
  }

  // Keys for storage
  static const String _userKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userIdKey = 'user_id';
  static const String _emailKey = 'user_email';
  static const String _nameKey = 'user_name';
  static const String _profileImageKey = 'profile_image';
  static const String _rememberMeKey = 'remember_me';
  static const String _savedEmailKey = 'saved_email';

  // User authentication data
  Future<void> saveUserData({
    required String userId,
    required String email,
    required String name,
    String? profileImage,
  }) async {
    final p = await prefs;
    await p.setBool(_isLoggedInKey, true);
    await p.setString(_userIdKey, userId);
    await p.setString(_emailKey, email);
    await p.setString(_nameKey, name);
    if (profileImage != null) {
      await p.setString(_profileImageKey, profileImage);
    }
  }

  Future<void> saveUserJson(Map<String, dynamic> userData) async {
    final p = await prefs;
    await p.setString(_userKey, json.encode(userData));
    await p.setBool(_isLoggedInKey, true);
  }

  Future<Map<String, dynamic>?> getUserData() async {
    final p = await prefs;
    final userDataString = p.getString(_userKey);
    if (userDataString != null) {
      return json.decode(userDataString);
    }
    return null;
  }

  Future<bool> get isLoggedIn async {
    final p = await prefs;
    return p.getBool(_isLoggedInKey) ?? false;
  }

  Future<String?> get userId async {
    final p = await prefs;
    return p.getString(_userIdKey);
  }

  Future<String?> get userEmail async {
    final p = await prefs;
    return p.getString(_emailKey);
  }

  Future<String?> get userName async {
    final p = await prefs;
    return p.getString(_nameKey);
  }

  Future<String?> get profileImage async {
    final p = await prefs;
    return p.getString(_profileImageKey);
  }

  // Remember me functionality
  Future<void> saveRememberMe(bool remember, String? email) async {
    final p = await prefs;
    await p.setBool(_rememberMeKey, remember);
    if (remember && email != null) {
      await p.setString(_savedEmailKey, email);
    } else {
      await p.remove(_savedEmailKey);
    }
  }

  Future<bool> get rememberMe async {
    final p = await prefs;
    return p.getBool(_rememberMeKey) ?? false;
  }

  Future<String?> get savedEmail async {
    final p = await prefs;
    return p.getString(_savedEmailKey);
  }

  // Clear all user data
  Future<void> clearUserData() async {
    final p = await prefs;
    await p.remove(_userKey);
    await p.remove(_isLoggedInKey);
    await p.remove(_userIdKey);
    await p.remove(_emailKey);
    await p.remove(_nameKey);
    await p.remove(_profileImageKey);
    // Keep remember me and saved email if user wants to remember
    final remember = await rememberMe;
    if (!remember) {
      await p.remove(_rememberMeKey);
      await p.remove(_savedEmailKey);
    }
  }

  // Generic storage methods
  Future<void> setString(String key, String value) async {
    final p = await prefs;
    await p.setString(key, value);
  }

  Future<void> setBool(String key, bool value) async {
    final p = await prefs;
    await p.setBool(key, value);
  }

  Future<void> setInt(String key, int value) async {
    final p = await prefs;
    await p.setInt(key, value);
  }

  Future<void> setDouble(String key, double value) async {
    final p = await prefs;
    await p.setDouble(key, value);
  }

  Future<void> setStringList(String key, List<String> value) async {
    final p = await prefs;
    await p.setStringList(key, value);
  }

  Future<String?> getString(String key) async {
    final p = await prefs;
    return p.getString(key);
  }

  Future<bool?> getBool(String key) async {
    final p = await prefs;
    return p.getBool(key);
  }

  Future<int?> getInt(String key) async {
    final p = await prefs;
    return p.getInt(key);
  }

  Future<double?> getDouble(String key) async {
    final p = await prefs;
    return p.getDouble(key);
  }

  Future<List<String>?> getStringList(String key) async {
    final p = await prefs;
    return p.getStringList(key);
  }

  Future<void> remove(String key) async {
    final p = await prefs;
    await p.remove(key);
  }

  Future<void> clear() async {
    final p = await prefs;
    await p.clear();
  }

  Future<bool> containsKey(String key) async {
    final p = await prefs;
    return p.containsKey(key);
  }
}
