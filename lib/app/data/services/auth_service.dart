import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/user_model.dart';
import 'firebase_service.dart';
import 'storage_service.dart';

/// Production-ready Authentication Service
class AuthService extends GetxService {
  static AuthService get instance => Get.find();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final StorageService _storageService = StorageService.instance;

  // Observable user state
  final Rx<User?> _firebaseUser = Rx<User?>(null);
  final Rx<UserModel?> _userModel = Rx<UserModel?>(null);
  final RxBool _isInitialized = false.obs;
  final RxBool _isLoading = false.obs;

  // Getters
  User? get firebaseUser => _firebaseUser.value;
  UserModel? get userModel => _userModel.value;
  bool get isLoggedIn => _firebaseUser.value != null;
  bool get isInitialized => _isInitialized.value;
  bool get isLoading => _isLoading.value;
  String? get currentUserId => _firebaseUser.value?.uid;
  String get userEmail => _firebaseUser.value?.email ?? '';
  String get userName => _userModel.value?.name ?? '';

  // Streams for real-time updates
  Stream<User?> get authStateChanges => _firebaseService.authStateChanges;

  @override
  void onInit() {
    super.onInit();
    _initializeAuthService();
  }

  /// Initialize authentication service with production-ready error handling
  Future<void> _initializeAuthService() async {
    try {
      // Listen to auth state changes
      _firebaseUser.bindStream(_firebaseService.authStateChanges);

      // Listen to user changes and fetch user data when auth state changes
      ever(_firebaseUser, _handleAuthStateChange);

      // Initialize current user if already signed in
      _firebaseUser.value = _firebaseService.currentUser;
      if (_firebaseUser.value != null) {
        await _fetchUserData(_firebaseUser.value!.uid);
      }

      _isInitialized.value = true;

      if (Get.isLogEnable) {
        Get.log('AuthService initialized successfully');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('AuthService initialization error: $e');
      }
      // Mark as initialized to prevent app crash
      _isInitialized.value = true;
      rethrow;
    }
  }

  // Handle auth state changes
  void _handleAuthStateChange(User? user) async {
    if (user != null) {
      // User is logged in, fetch user data
      await _fetchUserData(user.uid);
    } else {
      // User is not logged in, clear user data
      _userModel.value = null;
      // Clear storage data in background
      _storageService.clearUserData().catchError((e) {
        if (Get.isLogEnable) {
          Get.log('Error clearing storage: $e');
        }
      });
    }
  }

  /// Fetch user data from Firestore
  Future<void> _fetchUserData(String uid) async {
    try {
      final doc = await _firebaseService.getUserDocument(uid);

      if (doc.exists && doc.data() != null) {
        _userModel.value = UserModel.fromFirestore(doc);

        // Save to storage for offline access
        if (_userModel.value != null) {
          await _storageService.saveUserJson(_userModel.value!.toJson());
        }

        if (Get.isLogEnable) {
          Get.log('User data fetched successfully for uid: $uid');
        }
      } else {
        // User document doesn't exist in Firestore
        if (Get.isLogEnable) {
          Get.log('User document not found in Firestore for uid: $uid');
        }
        // Try to load from local storage as fallback
        await _loadUserFromStorage();
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching user data: $e');
      }
      // Try to load from local storage as fallback
      await _loadUserFromStorage();
    }
  }

  /// Load user data from local storage as fallback
  Future<void> _loadUserFromStorage() async {
    try {
      final userData = await _storageService.getUserData();
      if (userData != null) {
        _userModel.value = UserModel.fromJson(userData);
        if (Get.isLogEnable) {
          Get.log('User data loaded from local storage');
        }
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error loading user data from storage: $e');
      }
    }
  }

  /// Sign up with email and password - Production ready
  Future<UserModel?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _isLoading.value = true;

      // Validate inputs
      if (email.trim().isEmpty || password.isEmpty || name.trim().isEmpty) {
        throw 'All fields are required';
      }

      if (!_firebaseService.isValidEmail(email)) {
        throw 'Please enter a valid email address';
      }

      if (!_firebaseService.isValidPassword(password)) {
        throw 'Password must be at least 6 characters long';
      }

      // Create user with Firebase Auth
      final UserCredential userCredential = await _firebaseService
          .createUserWithEmailAndPassword(email: email, password: password);

      final User? user = userCredential.user;
      if (user != null) {
        // Update display name
        await _firebaseService.updateUserDisplayName(name.trim());

        // Create user document in Firestore
        final userModel = UserModel(
          uid: user.uid,
          email: email.trim().toLowerCase(),
          name: name.trim(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save to Firestore
        await _firebaseService.createUserDocument(userModel);

        // Save to local storage
        await _storageService.saveUserJson(userModel.toJson());

        _userModel.value = userModel;

        if (Get.isLogEnable) {
          Get.log('User registered successfully: ${userModel.email}');
        }

        return userModel;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      final errorMessage = _firebaseService.getAuthErrorMessage(e);
      if (Get.isLogEnable) {
        Get.log('Firebase Auth Exception: ${e.code} - ${e.message}');
      }
      throw errorMessage;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Sign up failed: $e');
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Sign in with email and password - Production ready
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _isLoading.value = true;

      // Validate inputs
      if (email.trim().isEmpty || password.isEmpty) {
        throw 'Email and password are required';
      }

      if (!_firebaseService.isValidEmail(email)) {
        throw 'Please enter a valid email address';
      }

      // Sign in with Firebase Auth
      final UserCredential userCredential = await _firebaseService
          .signInWithEmailAndPassword(email: email, password: password);

      final User? user = userCredential.user;
      if (user != null) {
        // Fetch user data from Firestore
        await _fetchUserData(user.uid);

        if (Get.isLogEnable) {
          Get.log('User signed in successfully: ${user.email}');
        }

        return _userModel.value;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      final errorMessage = _firebaseService.getAuthErrorMessage(e);
      if (Get.isLogEnable) {
        Get.log('Firebase Auth Exception: ${e.code} - ${e.message}');
      }
      throw errorMessage;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Sign in failed: $e');
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Sign out - Production ready
  Future<void> signOut() async {
    try {
      _isLoading.value = true;

      // Sign out from Firebase
      await _firebaseService.signOut();

      // Clear local data
      await _storageService.clearUserData();
      _userModel.value = null;

      if (Get.isLogEnable) {
        Get.log('User signed out successfully');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Sign out failed: $e');
      }
      // Force local cleanup even if Firebase sign out fails
      _userModel.value = null;
      await _storageService.clearUserData();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Reset password - Production ready
  Future<void> resetPassword(String email) async {
    try {
      _isLoading.value = true;

      if (email.trim().isEmpty) {
        throw 'Email is required';
      }

      if (!_firebaseService.isValidEmail(email)) {
        throw 'Please enter a valid email address';
      }

      // Send password reset email
      await _firebaseService.sendPasswordResetEmail(email);

      if (Get.isLogEnable) {
        Get.log('Password reset email sent to: $email');
      }
    } on FirebaseAuthException catch (e) {
      final errorMessage = _firebaseService.getAuthErrorMessage(e);
      if (Get.isLogEnable) {
        Get.log('Firebase Auth Exception: ${e.code} - ${e.message}');
      }
      throw errorMessage;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Reset password failed: $e');
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update user profile - Production ready
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      _isLoading.value = true;

      if (_firebaseUser.value == null) {
        throw 'User not authenticated';
      }

      // Update in Firestore
      await _firebaseService.updateUserDocument(
        updatedUser.uid,
        updatedUser.toFirestore(),
      );

      // Update local model
      _userModel.value = updatedUser;

      // Update local storage
      await _storageService.saveUserJson(updatedUser.toJson());

      if (Get.isLogEnable) {
        Get.log('User profile updated successfully');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Update profile failed: $e');
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update user profile fields - Production ready
  Future<void> updateUserProfileFields({
    String? name,
    String? profileImage,
    String? companyName,
    String? jobTitle,
    String? phoneNumber,
    String? bio,
    List<String>? skills,
    bool? isProfileComplete,
  }) async {
    try {
      _isLoading.value = true;

      final user = _firebaseUser.value;
      if (user == null) throw 'User not authenticated';

      final updates = <String, dynamic>{
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (name != null) {
        updates['name'] = name;
        await _firebaseService.updateUserDisplayName(name);
      }

      if (profileImage != null) {
        updates['profileImage'] = profileImage;
      }

      if (companyName != null) {
        updates['companyName'] = companyName;
      }

      if (jobTitle != null) {
        updates['jobTitle'] = jobTitle;
      }

      if (phoneNumber != null) {
        updates['phoneNumber'] = phoneNumber;
      }

      if (bio != null) {
        updates['bio'] = bio;
      }

      if (skills != null) {
        updates['skills'] = skills;
      }

      if (isProfileComplete != null) {
        updates['isProfileComplete'] = isProfileComplete;
      }

      // Update in Firestore
      await _firebaseService.updateUserDocument(user.uid, updates);

      // Refresh user data
      await _fetchUserData(user.uid);

      if (Get.isLogEnable) {
        Get.log('User profile fields updated successfully');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Update profile fields failed: $e');
      }
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }
}
