import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class GradientBackground extends StatelessWidget {
  final Widget child;
  final Gradient? gradient;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;

  const GradientBackground({
    super.key,
    required this.child,
    this.gradient,
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient ?? AppColors.primaryGradient,
      ),
      child: child,
    );
  }
}

class SecondaryGradientBackground extends StatelessWidget {
  final Widget child;

  const SecondaryGradientBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      gradient: AppColors.secondaryGradient,
      child: child,
    );
  }
}

class AccentGradientBackground extends StatelessWidget {
  final Widget child;

  const AccentGradientBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      gradient: AppColors.accentGradient,
      child: child,
    );
  }
}
