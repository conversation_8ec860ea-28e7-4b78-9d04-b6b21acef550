import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/project_service.dart';
import '../../../data/services/auth_service.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class CreateProjectController extends GetxController {
  static CreateProjectController get instance => Get.find();

  // Form key
  final GlobalKey<FormState> createProjectFormKey = GlobalKey<FormState>();

  // Text editing controllers
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();

  // Observable variables
  final RxString selectedVisibility = 'private'.obs;

  // Services
  final ProjectService _projectService = ProjectService.instance;
  final AuthService _authService = AuthService.instance;

  // Loading state
  bool get isLoading => _authService.isLoading;

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  /// Validate form
  bool _validateForm() {
    if (!createProjectFormKey.currentState!.validate()) {
      return false;
    }

    if (nameController.text.trim().isEmpty) {
      Helpers.showSnackbar(message: 'Project name is required', isError: true);
      return false;
    }

    return true;
  }

  /// Create project
  Future<void> createProject() async {
    if (!_validateForm()) return;

    try {
      final project = await _projectService.createProject(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        visibility: selectedVisibility.value,
      );

      Helpers.showSnackbar(
        message: 'Project "${project.name}" created successfully!',
        isError: false,
      );

      // Navigate back to projects page
      Get.back();

      // Optionally navigate to the project details
      // Get.toNamed(AppRoutes.projectDetails, arguments: project.id);
    } catch (e) {
      Helpers.showSnackbar(message: e.toString(), isError: true);
    }
  }

  /// Change project visibility
  void changeVisibility(String visibility) {
    selectedVisibility.value = visibility;
  }

  /// Navigate back
  void goBack() {
    Get.back();
  }
}
