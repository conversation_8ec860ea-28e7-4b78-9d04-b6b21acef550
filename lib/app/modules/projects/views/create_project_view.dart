import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/create_project_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';

class CreateProjectView extends GetView<CreateProjectController> {
  const CreateProjectView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Create Project'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.onSurface,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: controller.goBack,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: controller.createProjectForm<PERSON>ey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Create New Project',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Fill in the details below to create your new project',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.onSurface.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 32),

                // Project Name Field
                Text(
                  'Project Name',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                CustomTextField(
                  controller: controller.nameController,
                  hintText: 'Enter project name',
                  prefixIcon: Icons.folder_outlined,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Project name is required';
                    }
                    if (value.trim().length < 3) {
                      return 'Project name must be at least 3 characters';
                    }
                    if (value.trim().length > 50) {
                      return 'Project name must be less than 50 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Project Description Field
                Text(
                  'Description',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                CustomTextField(
                  controller: controller.descriptionController,
                  hintText: 'Enter project description (optional)',
                  prefixIcon: Icons.description_outlined,
                  maxLines: 4,
                  validator: (value) {
                    if (value != null && value.trim().length > 500) {
                      return 'Description must be less than 500 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Project Visibility
                Text(
                  'Project Visibility',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 12),
                Obx(
                  () => Column(
                    children: [
                      RadioListTile<String>(
                        title: const Text('Private'),
                        subtitle: const Text(
                          'Only invited members can see this project',
                        ),
                        value: 'private',
                        groupValue: controller.selectedVisibility.value,
                        onChanged: (value) {
                          if (value != null) {
                            controller.changeVisibility(value);
                          }
                        },
                        activeColor: AppColors.primary,
                      ),
                      RadioListTile<String>(
                        title: const Text('Public'),
                        subtitle: const Text(
                          'Anyone can discover and request to join',
                        ),
                        value: 'public',
                        groupValue: controller.selectedVisibility.value,
                        onChanged: (value) {
                          if (value != null) {
                            controller.changeVisibility(value);
                          }
                        },
                        activeColor: AppColors.primary,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Info Card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.accent.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.accent,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'You will be the owner and manager of this project. You can add team members later.',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppColors.accent),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),

                // Create Button
                Obx(
                  () => CustomButton(
                    text: 'Create Project',
                    onPressed: controller.createProject,
                    isLoading: controller.isLoading,
                    width: double.infinity,
                  ),
                ),
                const SizedBox(height: 16),

                // Cancel Button
                CustomButton(
                  text: 'Cancel',
                  onPressed: controller.goBack,
                  type: ButtonType.outline,
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
