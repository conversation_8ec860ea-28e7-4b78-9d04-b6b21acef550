import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';

class TasksController extends GetxController {
  static TasksController get instance => Get.find();

  final AuthService _authService = AuthService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList assignedTasks = [].obs;
  final RxList createdTasks = [].obs;
  final RxString selectedFilter = 'all'.obs;

  // Filter options
  final List<String> filterOptions = [
    'all',
    'pending',
    'in_progress',
    'completed',
  ];

  // Get current user
  get currentUser => _authService.userModel;

  @override
  void onInit() {
    super.onInit();
    _loadTasks();
  }

  // Load tasks
  Future<void> _loadTasks() async {
    try {
      isLoading.value = true;

      // TODO: Load user's tasks from Firestore
      // This will be implemented when we create the task service

      await Future.delayed(const Duration(seconds: 1)); // Simulate loading
    } catch (e) {
      Get.snackbar('Error', 'Failed to load tasks: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh tasks
  Future<void> refreshTasks() async {
    await _loadTasks();
  }

  // Change filter
  void changeFilter(String filter) {
    selectedFilter.value = filter;
    // TODO: Filter tasks based on selected filter
  }

  // Get filter display name
  String getFilterDisplayName(String filter) {
    switch (filter) {
      case 'all':
        return 'All Tasks';
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return 'All Tasks';
    }
  }
}
