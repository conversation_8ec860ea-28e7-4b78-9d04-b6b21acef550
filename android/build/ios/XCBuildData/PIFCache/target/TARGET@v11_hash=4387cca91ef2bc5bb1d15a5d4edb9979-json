{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873ed3e53edfa8ab8acba1e91ca59518d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1ed77b27644e4cf571a94c1f2674ef4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a086e6836c3be10c6047846c8729388", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981fd69dc68044f326f89f4cec79f59dab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a086e6836c3be10c6047846c8729388", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a43c164e8e6ef6b77d620362da4de7c6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f98aa8306d5b908cd6608651cc0750e7", "guid": "bfdfe7dc352907fc980b868725387e98210e8aef5099f3828a077ba4b5845a64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da2adc4e54d87f1e2135ea578813c044", "guid": "bfdfe7dc352907fc980b868725387e9837abe3c48957d76f07a8d587cbe9d3d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edce6cf5e153df6f7eca388fd4458f87", "guid": "bfdfe7dc352907fc980b868725387e98bc9c3baea80e50dd7bdd13482dbd77a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870349188d3468984cc81501660dd180e", "guid": "bfdfe7dc352907fc980b868725387e988bdead8abf2b4915d175cd92ce709f51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98963b66f8010c834759512e0073ab6dd9", "guid": "bfdfe7dc352907fc980b868725387e988dbbfd02151e78e585a38a0d427892a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873a838fc973da295be05e6a0c4da588a", "guid": "bfdfe7dc352907fc980b868725387e98da30ea80da162bd9b960a256877ed6ee", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a905f41b6160556057168fe091137dd7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9806a4577056b49415815056c70d987dda", "guid": "bfdfe7dc352907fc980b868725387e988c3dd6c7d70b1f3c0e405e8eb3f8c890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b574761f008966dffc072086b281440e", "guid": "bfdfe7dc352907fc980b868725387e981090cbe6392157acfb6f6bbee0b0c504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc19a86e5180200952a723aef131265d", "guid": "bfdfe7dc352907fc980b868725387e98f130b6eb6b97a2967a3672da3087e6cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983926455c61cd79c79445a6a390c1a583", "guid": "bfdfe7dc352907fc980b868725387e984072f6fc03a6b109b8655c9da3d2b97a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983979ddf17170bfdb13b7c564fd3bbef2", "guid": "bfdfe7dc352907fc980b868725387e980ea8bd8c59ebf809e5dc454ccf1465f0"}], "guid": "bfdfe7dc352907fc980b868725387e98eb14f7e0b4ebf734a6cee5732db4cb38", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e989da3954c5c1c6d215fe042b67b1ad219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e9863ce4becbd8e05610777622f609cdcb6"}], "guid": "bfdfe7dc352907fc980b868725387e981319dd5a035d039e2ebbe26df7500672", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9876a5966909db9e2d9a56aad3e42e3641", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9817cd657a79b06472e361bf25a413d99b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}