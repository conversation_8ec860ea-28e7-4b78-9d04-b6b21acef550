import 'package:get/get.dart';
import '../controllers/main_controller.dart';
import '../../home/<USER>/home_controller.dart';
import '../../projects/controllers/projects_controller.dart';
import '../../tasks/controllers/tasks_controller.dart';
import '../../profile/controllers/profile_controller.dart';

class MainBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MainController>(() => MainController());
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<ProjectsController>(() => ProjectsController());
    Get.lazyPut<TasksController>(() => TasksController());
    Get.lazyPut<ProfileController>(() => ProfileController());
  }
}
