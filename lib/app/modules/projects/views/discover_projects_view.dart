import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/discover_projects_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../data/models/project_model.dart';

class DiscoverProjectsView extends GetView<DiscoverProjectsController> {
  const DiscoverProjectsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Discover Projects'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.onSurface,
      ),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Search Bar
            Container(
              padding: const EdgeInsets.all(16),
              color: AppColors.surface,
              child: <PERSON><PERSON><PERSON>(
                controller: controller.searchController,
                onChanged: controller.updateSearchQuery,
                decoration: InputDecoration(
                  hintText: 'Search public projects...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: Obx(() {
                    if (controller.searchQuery.value.isNotEmpty) {
                      return IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: controller.clearSearch,
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: AppColors.background,
                ),
              ),
            ),

            // Projects List
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                final filteredProjects = controller.filteredProjects;

                if (filteredProjects.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: controller.refreshProjects,
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredProjects.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final project = filteredProjects[index];
                      return _buildProjectCard(project);
                    },
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectCard(ProjectModel project) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.onSurface.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Project Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      project.name,
                      style: Get.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.public,
                          size: 16,
                          color: AppColors.accent,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Public Project',
                          style: Get.textTheme.bodySmall?.copyWith(
                            color: AppColors.accent,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: project.isActive ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  project.status.toUpperCase(),
                  style: Get.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          // Project Description
          if (project.description.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              project.description,
              style: Get.textTheme.bodyLarge?.copyWith(
                color: AppColors.onSurface.withOpacity(0.8),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          const SizedBox(height: 16),

          // Project Stats
          Row(
            children: [
              _buildStatChip(
                Icons.people_outline,
                '${project.totalMembers} members',
                AppColors.primary,
              ),
              const SizedBox(width: 12),
              _buildStatChip(
                Icons.assignment_outlined,
                '${project.stats.totalTasks} tasks',
                AppColors.secondary,
              ),
              const SizedBox(width: 12),
              if (project.stats.totalTasks > 0)
                _buildStatChip(
                  Icons.check_circle_outline,
                  '${project.stats.completionPercentage.toInt()}% done',
                  Colors.green,
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Project Info
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: AppColors.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Text(
                'Created ${_formatDate(project.createdAt)}',
                style: Get.textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurface.withOpacity(0.6),
                ),
              ),
              const Spacer(),
              Icon(
                Icons.update,
                size: 16,
                color: AppColors.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Text(
                'Updated ${_formatDate(project.updatedAt)}',
                style: Get.textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Join Button
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'Request to Join',
              onPressed: () => controller.sendJoinRequest(project),
              icon: Icons.group_add,
              type: ButtonType.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: Get.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: AppColors.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 24),
            Text(
              'No Public Projects Found',
              style: Get.textTheme.headlineSmall?.copyWith(
                color: AppColors.onSurface.withOpacity(0.6),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              controller.searchQuery.value.isNotEmpty
                  ? 'Try adjusting your search terms'
                  : 'There are no public projects available at the moment',
              style: Get.textTheme.bodyLarge?.copyWith(
                color: AppColors.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Refresh',
              onPressed: controller.refreshProjects,
              icon: Icons.refresh,
              type: ButtonType.outline,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
