{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8498ee0d6e2dd5181516218d7c4ec39", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseAuth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseAuth", "INFOPLIST_FILE": "Target Support Files/FirebaseAuth/ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseAuth_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986792a5b28ad627c1358624e2ca67eb76", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c999c45ee8fcfa7b5412663eeabcc39", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseAuth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseAuth", "INFOPLIST_FILE": "Target Support Files/FirebaseAuth/ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "FirebaseAuth_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9830ae52fea5f0a711749bec522ef251eb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c999c45ee8fcfa7b5412663eeabcc39", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseAuth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseAuth", "INFOPLIST_FILE": "Target Support Files/FirebaseAuth/ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "FirebaseAuth_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988ade14f000179277c4cddd6a2d1f933b", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980cb1a774b480b635fb7f26c72196c50d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983903c3c15d7e96493e25d45272a1bd4d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d84eeda3b01d0b9ff47b05395920bf1", "guid": "bfdfe7dc352907fc980b868725387e98f6e5f4134fb9fc686c9bd1c66412ccdd"}], "guid": "bfdfe7dc352907fc980b868725387e9842323dd1e5c37f2655100f2cc383a4a6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98205354208adeebae46380f8f82956de4", "name": "FirebaseAuth-FirebaseAuth_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9870db214ed2dafc4df91a7caff7044acf", "name": "FirebaseAuth_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}