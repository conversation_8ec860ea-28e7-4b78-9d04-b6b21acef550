import 'package:cloud_firestore/cloud_firestore.dart';

class ProjectStats {
  final int totalTasks;
  final int completedTasks;
  final int totalMembers;

  ProjectStats({
    this.totalTasks = 0,
    this.completedTasks = 0,
    this.totalMembers = 0,
  });

  factory ProjectStats.fromMap(Map<String, dynamic>? data) {
    if (data == null) return ProjectStats();
    return ProjectStats(
      totalTasks: data['totalTasks'] ?? 0,
      completedTasks: data['completedTasks'] ?? 0,
      totalMembers: data['totalMembers'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'totalMembers': totalMembers,
    };
  }

  double get completionPercentage {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }
}

class ProjectModel {
  final String id;
  final String name;
  final String description;
  final String ownerId;
  final List<String> members;
  final List<String> managers;
  final String visibility; // 'public' or 'private'
  final String status; // 'active', 'completed', 'archived'
  final ProjectStats stats;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProjectModel({
    required this.id,
    required this.name,
    required this.description,
    required this.ownerId,
    required this.members,
    required this.managers,
    this.visibility = 'private',
    this.status = 'active',
    ProjectStats? stats,
    required this.createdAt,
    required this.updatedAt,
  }) : stats = stats ?? ProjectStats();

  // Factory constructor to create ProjectModel from Map
  factory ProjectModel.fromMap(Map<String, dynamic> data, String id) {
    return ProjectModel(
      id: id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      ownerId: data['ownerId'] ?? '',
      members: List<String>.from(data['members'] ?? []),
      managers: List<String>.from(data['managers'] ?? []),
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? DateTime.parse(data['updatedAt'])
          : DateTime.now(),
    );
  }

  // Factory constructor to create ProjectModel from JSON
  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      ownerId: json['ownerId'] ?? '',
      members: List<String>.from(json['members'] ?? []),
      managers: List<String>.from(json['managers'] ?? []),
      visibility: json['visibility'] ?? 'private',
      status: json['status'] ?? 'active',
      stats: ProjectStats.fromMap(json['stats']),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  // Convert ProjectModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'ownerId': ownerId,
      'members': members,
      'managers': managers,
      'visibility': visibility,
      'status': status,
      'stats': stats.toMap(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Convert ProjectModel to Map (without id as it's the document ID)
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'ownerId': ownerId,
      'members': members,
      'managers': managers,
      'visibility': visibility,
      'status': status,
      'stats': stats.toMap(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'ownerId': ownerId,
      'members': members,
      'managers': managers,
      'visibility': visibility,
      'status': status,
      'stats': stats.toMap(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  // Create from Firestore document
  factory ProjectModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data()!;
    return ProjectModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      ownerId: data['ownerId'] ?? '',
      members: List<String>.from(data['members'] ?? []),
      managers: List<String>.from(data['managers'] ?? []),
      visibility: data['visibility'] ?? 'private',
      status: data['status'] ?? 'active',
      stats: ProjectStats.fromMap(data['stats']),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Create a copy of ProjectModel with updated fields
  ProjectModel copyWith({
    String? id,
    String? name,
    String? description,
    String? ownerId,
    List<String>? members,
    List<String>? managers,
    String? visibility,
    String? status,
    ProjectStats? stats,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      members: members ?? List<String>.from(this.members),
      managers: managers ?? List<String>.from(this.managers),
      visibility: visibility ?? this.visibility,
      status: status ?? this.status,
      stats: stats ?? this.stats,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool isOwner(String userId) => ownerId == userId;

  bool isManager(String userId) => managers.contains(userId) || isOwner(userId);

  bool isMember(String userId) => members.contains(userId) || isManager(userId);

  String getUserRole(String userId) {
    if (isOwner(userId)) return 'owner';
    if (managers.contains(userId)) return 'manager';
    if (members.contains(userId)) return 'developer';
    return 'none';
  }

  int get totalMembers => members.length;
  bool get isPublic => visibility == 'public';
  bool get isPrivate => visibility == 'private';
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isArchived => status == 'archived';
  double get completionPercentage => stats.completionPercentage;

  int get totalManagers => managers.length;

  @override
  String toString() {
    return 'ProjectModel(id: $id, name: $name, description: $description, ownerId: $ownerId, members: $members, managers: $managers, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProjectModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.ownerId == ownerId &&
        other.members.toString() == members.toString() &&
        other.managers.toString() == managers.toString() &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        ownerId.hashCode ^
        members.hashCode ^
        managers.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
