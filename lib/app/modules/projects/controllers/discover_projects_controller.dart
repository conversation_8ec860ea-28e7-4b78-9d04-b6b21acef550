import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/join_request_service.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/project_model.dart';
import '../../../core/utils/helpers.dart';

class DiscoverProjectsController extends GetxController {
  static DiscoverProjectsController get instance => Get.find();

  // Services
  final JoinRequestService _joinRequestService = JoinRequestService.instance;
  final AuthService _authService = AuthService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<ProjectModel> publicProjects = <ProjectModel>[].obs;
  final RxString searchQuery = ''.obs;

  // Text controllers
  final searchController = TextEditingController();

  // Get current user
  get currentUser => _authService.userModel;

  @override
  void onInit() {
    super.onInit();
    _loadPublicProjects();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Load public projects
  Future<void> _loadPublicProjects() async {
    try {
      isLoading.value = true;
      final projects = await _joinRequestService.getPublicProjects();
      publicProjects.value = projects;
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to load public projects: $e',
        isError: true,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Get filtered projects based on search query
  List<ProjectModel> get filteredProjects {
    if (searchQuery.value.isEmpty) {
      return publicProjects;
    }
    
    final query = searchQuery.value.toLowerCase();
    return publicProjects.where((project) {
      return project.name.toLowerCase().contains(query) ||
             project.description.toLowerCase().contains(query);
    }).toList();
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// Send join request to a project
  Future<void> sendJoinRequest(ProjectModel project) async {
    try {
      // Show join request dialog
      final result = await Get.dialog<String>(
        _buildJoinRequestDialog(project),
      );

      if (result != null && result.isNotEmpty) {
        await _joinRequestService.sendJoinRequest(
          projectId: project.id,
          message: result,
        );

        Helpers.showSnackbar(
          message: 'Join request sent to "${project.name}"',
          isError: false,
        );
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to send join request: $e',
        isError: true,
      );
    }
  }

  /// Build join request dialog
  Widget _buildJoinRequestDialog(ProjectModel project) {
    final messageController = TextEditingController();
    
    return AlertDialog(
      title: Text('Join "${project.name}"'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Send a message to the project owner explaining why you want to join:',
            style: Get.textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: messageController,
            maxLines: 4,
            decoration: const InputDecoration(
              hintText: 'I would like to join this project because...',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final message = messageController.text.trim();
            if (message.isNotEmpty) {
              Get.back(result: message);
            } else {
              Helpers.showSnackbar(
                message: 'Please enter a message',
                isError: true,
              );
            }
          },
          child: const Text('Send Request'),
        ),
      ],
    );
  }

  /// Refresh public projects
  Future<void> refreshProjects() async {
    await _loadPublicProjects();
  }

  /// Navigate back
  void goBack() {
    Get.back();
  }
}
