import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../routes/app_routes.dart';

class SplashController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  void _initializeApp() async {
    try {
      // Add a small delay for splash screen effect
      await Future.delayed(const Duration(seconds: 2));

      // Get the auth service instance
      final authService = Get.find<AuthService>();

      // Check if user is logged in
      if (authService.isLoggedIn) {
        // Check if profile is complete
        final user = authService.userModel;
        if (user != null && !user.isProfileComplete) {
          // Profile not complete, go to profile setup
          Get.offAllNamed(AppRoutes.profileSetup);
        } else {
          // User is logged in and profile is complete, go to main app
          Get.offAllNamed(AppRoutes.main);
        }
      } else {
        // User is not logged in, go to login
        Get.offAllNamed(AppRoutes.login);
      }
    } catch (e) {
      // If there's any error, go to login
      if (Get.isLogEnable) {
        Get.log('Splash initialization error: $e');
      }
      Get.offAllNamed(AppRoutes.login);
    }
  }
}
