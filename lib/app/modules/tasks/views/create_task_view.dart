import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/create_task_controller.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_button.dart';

class CreateTaskView extends GetView<CreateTaskController> {
  const CreateTaskView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Create Task'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.onSurface,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: controller.goBack,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: controller.createTaskForm<PERSON>ey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Create New Task',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Fill in the details below to create a new task',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.onSurface.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 32),

                // Task Title Field
                Text(
                  'Task Title',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                CustomTextField(
                  controller: controller.titleController,
                  hintText: 'Enter task title',
                  prefixIcon: Icons.task_outlined,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Task title is required';
                    }
                    if (value.trim().length < 3) {
                      return 'Task title must be at least 3 characters';
                    }
                    if (value.trim().length > 100) {
                      return 'Task title must be less than 100 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Task Description Field
                Text(
                  'Description',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                CustomTextField(
                  controller: controller.descriptionController,
                  hintText: 'Enter task description',
                  prefixIcon: Icons.description_outlined,
                  maxLines: 4,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Task description is required';
                    }
                    if (value.trim().length < 10) {
                      return 'Description must be at least 10 characters';
                    }
                    if (value.trim().length > 1000) {
                      return 'Description must be less than 1000 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Priority Selection
                Text(
                  'Priority',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.borderColor),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: DropdownButton<String>(
                    value: controller.selectedPriority.value,
                    isExpanded: true,
                    underline: const SizedBox.shrink(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.changePriority(value);
                      }
                    },
                    items: controller.priorityOptions.map((priority) {
                      return DropdownMenuItem(
                        value: priority,
                        child: Text(controller.getPriorityDisplayName(priority)),
                      );
                    }).toList(),
                  ),
                )),
                const SizedBox(height: 24),

                // Assignee Selection
                Text(
                  'Assign To (Optional)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() {
                  if (controller.isLoadingMembers.value) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.borderColor),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Loading members...'),
                        ],
                      ),
                    );
                  }

                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.borderColor),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: DropdownButton<String>(
                      value: controller.selectedAssignee.value.isEmpty 
                          ? null 
                          : controller.selectedAssignee.value,
                      isExpanded: true,
                      underline: const SizedBox.shrink(),
                      hint: const Text('Select assignee (optional)'),
                      onChanged: (value) {
                        controller.changeAssignee(value ?? '');
                      },
                      items: [
                        const DropdownMenuItem(
                          value: '',
                          child: Text('No assignee'),
                        ),
                        ...controller.projectMembers.map((member) {
                          return DropdownMenuItem(
                            value: member.uid,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 12,
                                  backgroundImage: member.profileImage != null
                                      ? NetworkImage(member.profileImage!)
                                      : null,
                                  child: member.profileImage == null
                                      ? Text(
                                          member.name.isNotEmpty 
                                              ? member.name[0].toUpperCase()
                                              : '?',
                                          style: const TextStyle(fontSize: 12),
                                        )
                                      : null,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    member.name,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  );
                }),
                const SizedBox(height: 24),

                // Image Upload Section
                Text(
                  'Attach Image (Optional)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() => controller.selectedImage.value != null
                    ? _buildSelectedImage()
                    : _buildImageUploadButton()),
                const SizedBox(height: 40),

                // Create Button
                Obx(
                  () => CustomButton(
                    text: 'Create Task',
                    onPressed: controller.createTask,
                    isLoading: controller.isLoading.value,
                    width: double.infinity,
                  ),
                ),
                const SizedBox(height: 16),

                // Cancel Button
                CustomButton(
                  text: 'Cancel',
                  onPressed: controller.goBack,
                  type: ButtonType.outline,
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageUploadButton() {
    return GestureDetector(
      onTap: controller.showImagePickerOptions,
      child: Container(
        height: 120,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.borderColor,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 40,
              color: AppColors.onSurface.withOpacity(0.6),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap to add image',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedImage() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderColor),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              controller.selectedImage.value!,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: controller.removeSelectedImage,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 8,
            right: 8,
            child: GestureDetector(
              onTap: controller.showImagePickerOptions,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
