import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/user_model.dart';
import '../../core/constants/app_constants.dart';

/// Production-ready Firebase service for authentication and Firestore operations
class FirebaseService extends GetxService {
  static FirebaseService get instance => Get.find();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Production configuration
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);
  static const Duration _operationTimeout = Duration(seconds: 30);

  @override
  void onInit() {
    super.onInit();
    _configureFirestore();
  }

  /// Configure Firestore settings for production
  void _configureFirestore() {
    try {
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Firestore configuration error: $e');
      }
    }
  }

  /// Create user account with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return await _executeWithRetry<UserCredential>(
      operation: () => _auth.createUserWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      ),
      operationName: 'createUserWithEmailAndPassword',
    );
  }

  /// Sign in user with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    return await _executeWithRetry<UserCredential>(
      operation: () => _auth.signInWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      ),
      operationName: 'signInWithEmailAndPassword',
    );
  }

  /// Sign out current user
  Future<void> signOut() async {
    return await _executeWithRetry<void>(
      operation: () => _auth.signOut(),
      operationName: 'signOut',
    );
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    return await _executeWithRetry<void>(
      operation: () => _auth.sendPasswordResetEmail(
        email: email.trim().toLowerCase(),
      ),
      operationName: 'sendPasswordResetEmail',
    );
  }

  /// Update user display name
  Future<void> updateUserDisplayName(String displayName) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('No authenticated user');

    return await _executeWithRetry<void>(
      operation: () => user.updateDisplayName(displayName.trim()),
      operationName: 'updateUserDisplayName',
    );
  }

  /// Create user document in Firestore
  Future<void> createUserDocument(UserModel userModel) async {
    return await _executeWithRetry<void>(
      operation: () => _firestore
          .collection(AppConstants.usersCollection)
          .doc(userModel.uid)
          .set(userModel.toFirestore()),
      operationName: 'createUserDocument',
    );
  }

  /// Get user document from Firestore
  Future<DocumentSnapshot<Map<String, dynamic>>> getUserDocument(String uid) async {
    return await _executeWithRetry<DocumentSnapshot<Map<String, dynamic>>>(
      operation: () => _firestore
          .collection(AppConstants.usersCollection)
          .doc(uid)
          .get(const GetOptions(source: Source.serverAndCache)),
      operationName: 'getUserDocument',
    );
  }

  /// Update user document in Firestore
  Future<void> updateUserDocument(String uid, Map<String, dynamic> data) async {
    return await _executeWithRetry<void>(
      operation: () => _firestore
          .collection(AppConstants.usersCollection)
          .doc(uid)
          .update({
            ...data,
            'updatedAt': FieldValue.serverTimestamp(),
          }),
      operationName: 'updateUserDocument',
    );
  }

  /// Get current authenticated user
  User? get currentUser => _auth.currentUser;

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Check if user is authenticated
  bool get isAuthenticated => _auth.currentUser != null;

  /// Execute operation with retry logic and timeout
  Future<T> _executeWithRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
  }) async {
    Exception? lastException;

    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        return await operation().timeout(_operationTimeout);
      } on FirebaseAuthException catch (e) {
        // Don't retry auth exceptions as they're usually permanent
        if (Get.isLogEnable) {
          Get.log('$operationName failed with FirebaseAuthException: ${e.code} - ${e.message}');
        }
        rethrow;
      } on FirebaseException catch (e) {
        lastException = Exception('Firebase error: ${e.message}');
        if (Get.isLogEnable) {
          Get.log('$operationName attempt $attempt failed: ${e.message}');
        }
      } on TimeoutException catch (e) {
        lastException = Exception('Operation timeout: ${e.message}');
        if (Get.isLogEnable) {
          Get.log('$operationName attempt $attempt timed out');
        }
      } catch (e) {
        lastException = Exception('Unexpected error: $e');
        if (Get.isLogEnable) {
          Get.log('$operationName attempt $attempt failed with unexpected error: $e');
        }
      }

      // Don't retry on the last attempt
      if (attempt == _maxRetries) {
        break;
      }

      // Wait before retrying with exponential backoff
      await Future.delayed(_retryDelay * attempt);
    }

    // All attempts failed
    throw lastException ?? Exception('$operationName failed after $_maxRetries attempts');
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  /// Validate password strength
  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Get user-friendly error message from FirebaseAuthException
  String getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled. Contact support.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed. Contact support.';
      case 'requires-recent-login':
        return 'Please sign in again to complete this action.';
      case 'invalid-credential':
        return 'Invalid credentials. Please check your email and password.';
      case 'network-request-failed':
        return 'Network error. Please check your internet connection.';
      case 'invalid-api-key':
        return 'Configuration error. Please contact support.';
      case 'app-not-authorized':
        return 'App not authorized. Please contact support.';
      case 'project-not-found':
        return 'Service unavailable. Please contact support.';
      default:
        return e.message ?? 'An authentication error occurred. Please try again.';
    }
  }
}
