import 'package:cloud_firestore/cloud_firestore.dart';

enum JoinRequestStatus {
  pending,
  accepted,
  rejected;

  String get value {
    switch (this) {
      case JoinRequestStatus.pending:
        return 'pending';
      case JoinRequestStatus.accepted:
        return 'accepted';
      case JoinRequestStatus.rejected:
        return 'rejected';
    }
  }

  static JoinRequestStatus fromString(String status) {
    switch (status) {
      case 'pending':
        return JoinRequestStatus.pending;
      case 'accepted':
        return JoinRequestStatus.accepted;
      case 'rejected':
        return JoinRequestStatus.rejected;
      default:
        return JoinRequestStatus.pending;
    }
  }

  String get displayName {
    switch (this) {
      case JoinRequestStatus.pending:
        return 'Pending';
      case JoinRequestStatus.accepted:
        return 'Accepted';
      case JoinRequestStatus.rejected:
        return 'Rejected';
    }
  }
}

class JoinRequestModel {
  final String id;
  final String projectId;
  final String projectName;
  final String userId;
  final String userName;
  final String userEmail;
  final String message;
  final JoinRequestStatus status;
  final DateTime requestedAt;
  final DateTime? respondedAt;
  final String? respondedBy;

  JoinRequestModel({
    required this.id,
    required this.projectId,
    required this.projectName,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.message,
    required this.status,
    required this.requestedAt,
    this.respondedAt,
    this.respondedBy,
  });

  // Factory constructor to create JoinRequestModel from Map
  factory JoinRequestModel.fromMap(Map<String, dynamic> data, String id) {
    return JoinRequestModel(
      id: id,
      projectId: data['projectId'] ?? '',
      projectName: data['projectName'] ?? '',
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      message: data['message'] ?? '',
      status: JoinRequestStatus.fromString(data['status'] ?? 'pending'),
      requestedAt: data['requestedAt'] != null
          ? DateTime.parse(data['requestedAt'])
          : DateTime.now(),
      respondedAt: data['respondedAt'] != null
          ? DateTime.parse(data['respondedAt'])
          : null,
      respondedBy: data['respondedBy'],
    );
  }

  // Factory constructor to create JoinRequestModel from JSON
  factory JoinRequestModel.fromJson(Map<String, dynamic> json) {
    return JoinRequestModel(
      id: json['id'] ?? '',
      projectId: json['projectId'] ?? '',
      projectName: json['projectName'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userEmail: json['userEmail'] ?? '',
      message: json['message'] ?? '',
      status: JoinRequestStatus.fromString(json['status'] ?? 'pending'),
      requestedAt: json['requestedAt'] != null
          ? DateTime.parse(json['requestedAt'])
          : DateTime.now(),
      respondedAt: json['respondedAt'] != null
          ? DateTime.parse(json['respondedAt'])
          : null,
      respondedBy: json['respondedBy'],
    );
  }

  // Convert JoinRequestModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'projectName': projectName,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'message': message,
      'status': status.value,
      'requestedAt': requestedAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'respondedBy': respondedBy,
    };
  }

  // Convert JoinRequestModel to Map (without id as it's the document ID)
  Map<String, dynamic> toMap() {
    return {
      'projectId': projectId,
      'projectName': projectName,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'message': message,
      'status': status.value,
      'requestedAt': requestedAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'respondedBy': respondedBy,
    };
  }

  // Create a copy of JoinRequestModel with updated fields
  JoinRequestModel copyWith({
    String? id,
    String? projectId,
    String? projectName,
    String? userId,
    String? userName,
    String? userEmail,
    String? message,
    JoinRequestStatus? status,
    DateTime? requestedAt,
    DateTime? respondedAt,
    String? respondedBy,
  }) {
    return JoinRequestModel(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      projectName: projectName ?? this.projectName,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      message: message ?? this.message,
      status: status ?? this.status,
      requestedAt: requestedAt ?? this.requestedAt,
      respondedAt: respondedAt ?? this.respondedAt,
      respondedBy: respondedBy ?? this.respondedBy,
    );
  }

  // Convert JoinRequestModel to Firestore format (without id as it's the document ID)
  Map<String, dynamic> toFirestore() {
    return {
      'projectId': projectId,
      'projectName': projectName,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'message': message,
      'status': status.value,
      'requestedAt': requestedAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'respondedBy': respondedBy,
    };
  }

  // Create from Firestore document
  factory JoinRequestModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data()!;
    return JoinRequestModel(
      id: doc.id,
      projectId: data['projectId'] ?? '',
      projectName: data['projectName'] ?? '',
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      userEmail: data['userEmail'] ?? '',
      message: data['message'] ?? '',
      status: JoinRequestStatus.fromString(data['status'] ?? 'pending'),
      requestedAt: data['requestedAt'] != null
          ? DateTime.parse(data['requestedAt'])
          : DateTime.now(),
      respondedAt: data['respondedAt'] != null
          ? DateTime.parse(data['respondedAt'])
          : null,
      respondedBy: data['respondedBy'],
    );
  }

  // Helper methods
  bool isPending() => status == JoinRequestStatus.pending;

  bool isAccepted() => status == JoinRequestStatus.accepted;

  bool isRejected() => status == JoinRequestStatus.rejected;

  bool isResponded() => respondedAt != null;

  bool isRequestedBy(String userId) => this.userId == userId;

  @override
  String toString() {
    return 'JoinRequestModel(id: $id, projectId: $projectId, projectName: $projectName, userId: $userId, userName: $userName, userEmail: $userEmail, message: $message, status: $status, requestedAt: $requestedAt, respondedAt: $respondedAt, respondedBy: $respondedBy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JoinRequestModel &&
        other.id == id &&
        other.projectId == projectId &&
        other.projectName == projectName &&
        other.userId == userId &&
        other.userName == userName &&
        other.userEmail == userEmail &&
        other.message == message &&
        other.status == status &&
        other.requestedAt == requestedAt &&
        other.respondedAt == respondedAt &&
        other.respondedBy == respondedBy;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        projectId.hashCode ^
        projectName.hashCode ^
        userId.hashCode ^
        userName.hashCode ^
        userEmail.hashCode ^
        message.hashCode ^
        status.hashCode ^
        requestedAt.hashCode ^
        respondedAt.hashCode ^
        respondedBy.hashCode;
  }
}
