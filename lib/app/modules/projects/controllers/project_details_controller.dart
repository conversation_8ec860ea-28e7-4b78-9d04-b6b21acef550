import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../data/services/project_service.dart';
import '../../../data/services/task_service.dart';
import '../../../data/services/join_request_service.dart';
import '../../../data/services/auth_service.dart';
import '../../../data/models/project_model.dart';
import '../../../data/models/task_model.dart';
import '../../../data/models/join_request_model.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class ProjectDetailsController extends GetxController {
  static ProjectDetailsController get instance => Get.find();

  // Services
  final ProjectService _projectService = ProjectService.instance;
  final TaskService _taskService = TaskService.instance;
  final JoinRequestService _joinRequestService = JoinRequestService.instance;
  final AuthService _authService = AuthService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;
  final RxBool isTasksLoading = false.obs;
  final Rxn<ProjectModel> project = Rxn<ProjectModel>();
  final RxList<TaskModel> tasks = <TaskModel>[].obs;
  final RxList<JoinRequestModel> joinRequests = <JoinRequestModel>[].obs;
  final RxString selectedTaskFilter = 'all'.obs;

  // Stream subscriptions
  StreamSubscription<List<TaskModel>>? _tasksSubscription;
  StreamSubscription<List<JoinRequestModel>>? _joinRequestsSubscription;

  // Project ID
  String? projectId;

  // Filter options
  final List<String> taskFilterOptions = ['all', 'todo', 'in_progress', 'completed'];

  // Get current user
  get currentUser => _authService.userModel;

  @override
  void onInit() {
    super.onInit();
    projectId = Get.arguments as String?;
    if (projectId != null) {
      _loadProjectDetails();
    }
  }

  @override
  void onClose() {
    _tasksSubscription?.cancel();
    _joinRequestsSubscription?.cancel();
    super.onClose();
  }

  /// Load project details
  Future<void> _loadProjectDetails() async {
    try {
      isLoading.value = true;

      // Load project
      final projectData = await _projectService.getProject(projectId!);
      if (projectData == null) {
        Helpers.showSnackbar(
          message: 'Project not found',
          isError: true,
        );
        Get.back();
        return;
      }

      project.value = projectData;

      // Check if user has access
      if (!projectData.isMember(currentUser?.uid ?? '')) {
        Helpers.showSnackbar(
          message: 'You do not have access to this project',
          isError: true,
        );
        Get.back();
        return;
      }

      // Load tasks with real-time updates
      _loadTasks();

      // Load join requests if user is manager
      if (projectData.isManager(currentUser?.uid ?? '')) {
        _loadJoinRequests();
      }

    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to load project: $e',
        isError: true,
      );
      Get.back();
    } finally {
      isLoading.value = false;
    }
  }

  /// Load tasks with real-time updates
  void _loadTasks() {
    isTasksLoading.value = true;
    _tasksSubscription = _taskService.getProjectTasksStream(projectId!).listen(
      (taskList) {
        tasks.value = taskList;
        isTasksLoading.value = false;
      },
      onError: (error) {
        Helpers.showSnackbar(
          message: 'Error loading tasks: $error',
          isError: true,
        );
        isTasksLoading.value = false;
      },
    );
  }

  /// Load join requests with real-time updates
  void _loadJoinRequests() {
    _joinRequestsSubscription = _joinRequestService
        .getProjectJoinRequestsStream(projectId!)
        .listen(
      (requests) {
        joinRequests.value = requests;
      },
      onError: (error) {
        Helpers.showSnackbar(
          message: 'Error loading join requests: $error',
          isError: true,
        );
      },
    );
  }

  /// Get filtered tasks based on selected filter
  List<TaskModel> get filteredTasks {
    switch (selectedTaskFilter.value) {
      case 'todo':
        return tasks.where((task) => task.status == TaskStatus.todo).toList();
      case 'in_progress':
        return tasks.where((task) => task.status == TaskStatus.inProgress).toList();
      case 'completed':
        return tasks.where((task) => task.status == TaskStatus.completed).toList();
      default:
        return tasks;
    }
  }

  /// Change task filter
  void changeTaskFilter(String filter) {
    selectedTaskFilter.value = filter;
  }

  /// Navigate to create task
  void createTask() {
    Get.toNamed(AppRoutes.createTask, arguments: {
      'projectId': projectId,
      'project': project.value,
    });
  }

  /// Update task status
  Future<void> updateTaskStatus(TaskModel task, TaskStatus newStatus) async {
    try {
      await _taskService.updateTaskStatus(task.id, newStatus, projectId!);
      
      Helpers.showSnackbar(
        message: 'Task status updated to ${newStatus.displayName}',
        isError: false,
      );
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to update task status: $e',
        isError: true,
      );
    }
  }

  /// Delete task
  Future<void> deleteTask(TaskModel task) async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('Delete Task'),
          content: Text('Are you sure you want to delete "${task.title}"? This action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _taskService.deleteTask(task.id, projectId!);
        Helpers.showSnackbar(
          message: 'Task "${task.title}" deleted successfully',
          isError: false,
        );
      }
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to delete task: $e',
        isError: true,
      );
    }
  }

  /// Approve join request
  Future<void> approveJoinRequest(JoinRequestModel request) async {
    try {
      await _joinRequestService.approveJoinRequest(request.id);
      Helpers.showSnackbar(
        message: '${request.userName} has been added to the project',
        isError: false,
      );
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to approve join request: $e',
        isError: true,
      );
    }
  }

  /// Reject join request
  Future<void> rejectJoinRequest(JoinRequestModel request) async {
    try {
      await _joinRequestService.rejectJoinRequest(request.id);
      Helpers.showSnackbar(
        message: 'Join request from ${request.userName} has been rejected',
        isError: false,
      );
    } catch (e) {
      Helpers.showSnackbar(
        message: 'Failed to reject join request: $e',
        isError: true,
      );
    }
  }

  /// Navigate to task details
  void openTask(TaskModel task) {
    Get.toNamed(AppRoutes.taskDetails, arguments: {
      'taskId': task.id,
      'projectId': projectId,
    });
  }

  /// Navigate to project settings
  void openProjectSettings() {
    // TODO: Implement project settings
    Get.snackbar('Info', 'Project settings feature coming soon!');
  }

  /// Refresh project data
  Future<void> refreshProject() async {
    await _loadProjectDetails();
  }

  /// Check if current user can manage project
  bool get canManageProject {
    final currentUserId = currentUser?.uid;
    if (currentUserId == null || project.value == null) return false;
    return project.value!.isManager(currentUserId);
  }

  /// Check if current user is project owner
  bool get isProjectOwner {
    final currentUserId = currentUser?.uid;
    if (currentUserId == null || project.value == null) return false;
    return project.value!.isOwner(currentUserId);
  }

  /// Get task statistics
  Map<String, int> get taskStats {
    final todoCount = tasks.where((task) => task.status == TaskStatus.todo).length;
    final inProgressCount = tasks.where((task) => task.status == TaskStatus.inProgress).length;
    final completedCount = tasks.where((task) => task.status == TaskStatus.completed).length;
    
    return {
      'total': tasks.length,
      'todo': todoCount,
      'inProgress': inProgressCount,
      'completed': completedCount,
    };
  }
}
