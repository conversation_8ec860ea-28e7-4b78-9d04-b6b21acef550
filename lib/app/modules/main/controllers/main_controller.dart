import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';

class MainController extends GetxController {
  static MainController get instance => Get.find();

  final AuthService _authService = AuthService.instance;

  // Current tab index
  final RxInt currentIndex = 0.obs;

  // Tab titles
  final List<String> tabTitles = ['Home', 'My Projects', 'Tasks', 'Profile'];

  // Get current user
  get currentUser => _authService.userModel;

  // Change tab
  void changeTab(int index) {
    currentIndex.value = index;
  }

  // Get current tab title
  String get currentTabTitle => tabTitles[currentIndex.value];

  // Logout
  Future<void> logout() async {
    try {
      await _authService.signOut();
      // Navigation will be handled by AuthService automatically
    } catch (e) {
      Get.snackbar('Error', 'Failed to logout: $e');
    }
  }
}
