import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/auth_service.dart';
import '../../../core/utils/helpers.dart';
import '../../../routes/app_routes.dart';

class ProfileController extends GetxController {
  static ProfileController get instance => Get.find();

  final AuthService _authService = AuthService.instance;

  // Observable variables
  final RxBool isLoading = false.obs;

  // Get current user
  get currentUser => _authService.userModel;

  // Edit profile
  void editProfile() {
    // TODO: Navigate to edit profile screen
    Get.snackbar('Info', 'Edit profile feature coming soon!');
  }

  // Settings
  void openSettings() {
    // TODO: Navigate to settings screen
    Get.snackbar('Info', 'Settings feature coming soon!');
  }

  // Logout
  Future<void> logout() async {
    final confirmed = await Helpers.showConfirmationDialog(
      title: 'Logout',
      message: 'Are you sure you want to logout?',
      confirmText: 'Logout',
      cancelText: 'Cancel',
    );

    if (confirmed) {
      try {
        isLoading.value = true;
        await _authService.signOut();
        Get.offAllNamed(AppRoutes.login);
      } catch (e) {
        Helpers.showSnackbar(message: 'Failed to logout: $e', isError: true);
      } finally {
        isLoading.value = false;
      }
    }
  }

  // About app
  void showAbout() {
    Get.dialog(
      AlertDialog(
        title: const Text('About'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Project Management App'),
            const SizedBox(height: 8),
            const Text('Version 1.0.0'),
            const SizedBox(height: 8),
            Text(
              'A collaborative project management application built with Flutter and Firebase.',
              style: Get.textTheme.bodyMedium,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Close')),
        ],
      ),
    );
  }
}
