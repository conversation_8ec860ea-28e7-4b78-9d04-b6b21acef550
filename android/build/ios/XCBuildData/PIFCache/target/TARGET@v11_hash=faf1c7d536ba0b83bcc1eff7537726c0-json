{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0612bd76eae56d5f66513bf3c6dcb14", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98516d467f1e71d73190a1068232e6cc5c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5d87ab7e28ffc9ddd54590df1bcd359", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c2a16963839103212990ad21e7b8005", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5d87ab7e28ffc9ddd54590df1bcd359", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98407c1b2eff4b5acd780a5b62a83e60a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983772b6cbd661f172b0dfd82e27a17d9c", "guid": "bfdfe7dc352907fc980b868725387e9817f0c2c9e55cc451449fbeb39927cfb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af52fcdb11345666c8ef64cfc36b1861", "guid": "bfdfe7dc352907fc980b868725387e98f369eb2c177b4a50c905b6991c25840f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e976a6ea07644092a4ae6cd40760cd", "guid": "bfdfe7dc352907fc980b868725387e982701e00816a92b57cee1d966ae27bae1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e266f142a21dd113c9ec4e0aa8cd6ce2", "guid": "bfdfe7dc352907fc980b868725387e98c634e70debc1c9b69cda1021540519a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cedcb8a0e0aec355a2ea27f8e42e4e58", "guid": "bfdfe7dc352907fc980b868725387e98075c1cd01f52c0cc3a75b67416d01555", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9f5cb7a99b27fe5c2af06b5b5bd3b42", "guid": "bfdfe7dc352907fc980b868725387e98ea3fe584cbcfa7f37c2f84883d467589", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834259659167be2b2b83d099413d4fa1e", "guid": "bfdfe7dc352907fc980b868725387e9886a8c0c60d20d3bf3c2e34c84cc84a06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673e09e43d7cb6088072a9efa7b59549", "guid": "bfdfe7dc352907fc980b868725387e98eefcb2fec178d4e4350cffde854e201a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2c90070d3928a949e23f809b22de97", "guid": "bfdfe7dc352907fc980b868725387e98aae711169b5c02ea4231353ae7520c7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885f09002b5dad7d450a73a85ec94978", "guid": "bfdfe7dc352907fc980b868725387e9869a02651fd60880e28720aecd498a88a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e645f97f71c9305d9a04ed9fd65bc715", "guid": "bfdfe7dc352907fc980b868725387e98e9185ff6ad8edab26092662719a1ca78", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983748f68ed56773c33f2a23e1e9fe5b44", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983cfa77f81f7ad056d758ae339a977256", "guid": "bfdfe7dc352907fc980b868725387e9815774a2047f5511927fe3f1e14098437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882906aa2855eb64f706d5cdde5774545", "guid": "bfdfe7dc352907fc980b868725387e9851d1bf99a6263cbadff05bee0539f0c2"}], "guid": "bfdfe7dc352907fc980b868725387e98e21627e58de55ed9a37b64dcc6a22a58", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987950f3c5f1b32f453d8b426b686cf8bc"}], "guid": "bfdfe7dc352907fc980b868725387e98384be413055d37528850037d3bdccd3c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d455c88b4469c44a13d1abb50e7b3c47", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e981d5f3c19e42cc7dc120545e494dc3807", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}