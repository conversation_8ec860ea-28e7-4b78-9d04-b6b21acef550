import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/project_model.dart';
import '../models/user_model.dart';
import 'auth_service.dart';

/// Production-ready Project Service for Firebase operations
class ProjectService extends GetxService {
  static ProjectService get instance => Get.find();

  final AuthService _authService = AuthService.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference
  static const String _projectsCollection = 'projects';

  /// Create a new project
  Future<ProjectModel> createProject({
    required String name,
    required String description,
    String visibility = 'private',
  }) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      final now = DateTime.now();
      final project = ProjectModel(
        id: '', // Will be set by Firestore
        name: name.trim(),
        description: description.trim(),
        ownerId: currentUser.uid,
        members: [currentUser.uid], // Owner is automatically a member
        managers: [currentUser.uid], // Owner is automatically a manager
        visibility: visibility,
        status: 'active',
        createdAt: now,
        updatedAt: now,
      );

      // Add to Firestore
      final docRef = await _firestore
          .collection(_projectsCollection)
          .add(project.toFirestore());

      // Return project with the generated ID
      final createdProject = project.copyWith(id: docRef.id);

      if (Get.isLogEnable) {
        Get.log('Project created successfully: ${createdProject.id}');
      }

      return createdProject;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error creating project: $e');
      }
      rethrow;
    }
  }

  /// Get all projects for the current user
  Future<List<ProjectModel>> getUserProjects() async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Use simpler query without orderBy to avoid index requirement
      final querySnapshot = await _firestore
          .collection(_projectsCollection)
          .where('members', arrayContains: currentUser.uid)
          .get();

      final projects = querySnapshot.docs
          .map((doc) => ProjectModel.fromFirestore(doc))
          .toList();

      // Sort locally by updatedAt
      projects.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      if (Get.isLogEnable) {
        Get.log(
          'Fetched ${projects.length} projects for user: ${currentUser.uid}',
        );
      }

      return projects;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching user projects: $e');
      }
      rethrow;
    }
  }

  /// Get projects owned by the current user
  Future<List<ProjectModel>> getOwnedProjects() async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Use simpler query without orderBy to avoid index requirement
      final querySnapshot = await _firestore
          .collection(_projectsCollection)
          .where('ownerId', isEqualTo: currentUser.uid)
          .get();

      final projects = querySnapshot.docs
          .map((doc) => ProjectModel.fromFirestore(doc))
          .toList();

      // Sort locally by updatedAt
      projects.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      if (Get.isLogEnable) {
        Get.log(
          'Fetched ${projects.length} owned projects for user: ${currentUser.uid}',
        );
      }

      return projects;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching owned projects: $e');
      }
      rethrow;
    }
  }

  /// Get a specific project by ID
  Future<ProjectModel?> getProject(String projectId) async {
    try {
      final doc = await _firestore
          .collection(_projectsCollection)
          .doc(projectId)
          .get();

      if (doc.exists && doc.data() != null) {
        return ProjectModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching project: $e');
      }
      rethrow;
    }
  }

  /// Update a project
  Future<void> updateProject(ProjectModel project) async {
    try {
      final updatedProject = project.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_projectsCollection)
          .doc(project.id)
          .update(updatedProject.toFirestore());

      if (Get.isLogEnable) {
        Get.log('Project updated successfully: ${project.id}');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error updating project: $e');
      }
      rethrow;
    }
  }

  /// Delete a project
  Future<void> deleteProject(String projectId) async {
    try {
      await _firestore.collection(_projectsCollection).doc(projectId).delete();

      if (Get.isLogEnable) {
        Get.log('Project deleted successfully: $projectId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error deleting project: $e');
      }
      rethrow;
    }
  }

  /// Get real-time stream of user projects
  Stream<List<ProjectModel>> getUserProjectsStream() {
    final currentUser = _authService.userModel;
    if (currentUser == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection(_projectsCollection)
        .where('members', arrayContains: currentUser.uid)
        .snapshots()
        .map((snapshot) {
          final projects = snapshot.docs
              .map((doc) => ProjectModel.fromFirestore(doc))
              .toList();

          // Sort locally by updatedAt
          projects.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

          return projects;
        });
  }

  /// Add member to project
  Future<void> addMemberToProject(String projectId, String userId) async {
    try {
      await _firestore.collection(_projectsCollection).doc(projectId).update({
        'members': FieldValue.arrayUnion([userId]),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      if (Get.isLogEnable) {
        Get.log('Member added to project: $projectId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error adding member to project: $e');
      }
      rethrow;
    }
  }

  /// Remove member from project
  Future<void> removeMemberFromProject(String projectId, String userId) async {
    try {
      await _firestore.collection(_projectsCollection).doc(projectId).update({
        'members': FieldValue.arrayRemove([userId]),
        'managers': FieldValue.arrayRemove([userId]),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      if (Get.isLogEnable) {
        Get.log('Member removed from project: $projectId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error removing member from project: $e');
      }
      rethrow;
    }
  }

  /// Get project members with their user details
  Future<List<UserModel>> getProjectMembers(String projectId) async {
    try {
      // First get the project to get member IDs
      final projectDoc = await _firestore
          .collection(_projectsCollection)
          .doc(projectId)
          .get();

      if (!projectDoc.exists) {
        throw 'Project not found';
      }

      final projectData = projectDoc.data()!;
      final memberIds = List<String>.from(projectData['members'] ?? []);

      if (memberIds.isEmpty) {
        return [];
      }

      // Get user details for all members
      final List<UserModel> members = [];

      // Firestore 'in' query has a limit of 10, so we need to batch if more members
      const batchSize = 10;
      for (int i = 0; i < memberIds.length; i += batchSize) {
        final batch = memberIds.skip(i).take(batchSize).toList();

        final usersSnapshot = await _firestore
            .collection('users')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        final batchMembers = usersSnapshot.docs
            .map((doc) => UserModel.fromFirestore(doc))
            .toList();

        members.addAll(batchMembers);
      }

      if (Get.isLogEnable) {
        Get.log('Fetched ${members.length} members for project: $projectId');
      }

      return members;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching project members: $e');
      }
      rethrow;
    }
  }

  /// Check if user has access to project
  Future<bool> hasProjectAccess(String projectId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) return false;

      final project = await getProject(projectId);
      return project?.isMember(currentUser.uid) ?? false;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error checking project access: $e');
      }
      return false;
    }
  }
}
