{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98749d1cce24796b4a221b71b240b5f656", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98330f9d9738739dabd0c184f19f3c11ab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989179f84aed1548d28714bb070b3ebf28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806bbf216b995a7738af255b787190b4d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989179f84aed1548d28714bb070b3ebf28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b1fb82637c261eb6e5c73f1632653b3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff77fa72d584079534e4765259a2ba66", "guid": "bfdfe7dc352907fc980b868725387e982304aaee202a4e23e5b93f5deb77ce02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675f67c5f4604d339c0c3212bf26ce48", "guid": "bfdfe7dc352907fc980b868725387e98d5f2198737eb2b8c5ad43e31b3ef1565", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d595db4d7eb5db76f2aed4ebf7a5eff7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb9bf20ef68df83d8610bc44257ba17c", "guid": "bfdfe7dc352907fc980b868725387e98a47bc74510a529a3f8a39f8568c09faf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd91a4f9edbde20c0aa84cb55516dcb", "guid": "bfdfe7dc352907fc980b868725387e980494dbe8cf3f42a3f519a6958e760875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e24ab98290dc0553b9e5c566be4c0fed", "guid": "bfdfe7dc352907fc980b868725387e9817c078cd0ddbc55acd88be26f3fc3d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98badcca640f07895995dd0eae678958b8", "guid": "bfdfe7dc352907fc980b868725387e9829e68a6f667dd18eee147988427a75bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d44b18d085e6c70bb8292ceb333164", "guid": "bfdfe7dc352907fc980b868725387e98a52a84e2e33030f30f30a60574500be7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a6daa2b773a7faa3a6f0ecb6554bffd", "guid": "bfdfe7dc352907fc980b868725387e982a9452488fad678a65bbe14030dd33d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2085c9b5a57d38c35789ed3334b0b27", "guid": "bfdfe7dc352907fc980b868725387e9875dcd05d1d104c89b26fcb6c448b7127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f5a9a0149c33cef8209be365d08700", "guid": "bfdfe7dc352907fc980b868725387e988c7fcd3da2bd0f60d19deb79599b9dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113e22d338abe5699f8ffb739a78d794", "guid": "bfdfe7dc352907fc980b868725387e98c9622eb9e55e58687c383d67898230e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d28d2e003dd2832f5cf057adb516b9", "guid": "bfdfe7dc352907fc980b868725387e9812b5f51f81fdb8663e1a0026b1c56578"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daae20479e501218277cb4ca2403cdc7", "guid": "bfdfe7dc352907fc980b868725387e98beb09969ffbe79cbb158ec4446b59c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a051f3aa913b9f3c4e8031b2ea4c897", "guid": "bfdfe7dc352907fc980b868725387e980bd1b577c4a5872b324264e385105b0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6294ad5328833be4426e5573021e941", "guid": "bfdfe7dc352907fc980b868725387e9854e68a87882a2c27a4a7a6f210fcf3a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b1c18c19f0a18414dd3b472ae140f66", "guid": "bfdfe7dc352907fc980b868725387e98e2d6a57dc43b1d177941534d336a3488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bc73882eb77b1167081ddebc861e72", "guid": "bfdfe7dc352907fc980b868725387e98d4c37eb57d27ae7b026fadb39c028a26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb7df6061332db2186fdf60b8af4829", "guid": "bfdfe7dc352907fc980b868725387e989830335a0318e231877b71453ed104b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980164e4384426d062878b3e68377af61a", "guid": "bfdfe7dc352907fc980b868725387e98c0fc88b11fffb690ef430138c01fae5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d73ea2eb71e7a9e1f12004ba8e7920d", "guid": "bfdfe7dc352907fc980b868725387e98904f01c5d92dc7b0036315151cafffde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f8f9b84279158c8f17af4f15df3d4d", "guid": "bfdfe7dc352907fc980b868725387e9837d62e74549f2bccc928a99a9c5a6c45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2097ce4308e99425db6eedee7c7403", "guid": "bfdfe7dc352907fc980b868725387e981ac73d6a37a4c4c05837a67713796678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1615a200000b3384f25c3e776de44c4", "guid": "bfdfe7dc352907fc980b868725387e9858459cc7cf3b3c3c0a6246fb7a526b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcaeac6b535936cf0085fb133942f240", "guid": "bfdfe7dc352907fc980b868725387e98cce298d45087641a5efd1c1c5e507209"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f49a6258128e3a61111bdfb76543ec", "guid": "bfdfe7dc352907fc980b868725387e98ea69033906179ebe50e29b198f1a3954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dec0fb0c9ed91199a9d4c293e2aadc5", "guid": "bfdfe7dc352907fc980b868725387e98cb1d6ad38c436ff17481d14c01a7a9f0"}], "guid": "bfdfe7dc352907fc980b868725387e9884946b79ca2cf44e15e253a4f636975d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98c79e09cec189345879a601d2f97fb572"}], "guid": "bfdfe7dc352907fc980b868725387e985cc91e1cda93ad6c3f04a42458ec455d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988ae93ae0114f6d6dbdd7e9c1f0efd469", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}